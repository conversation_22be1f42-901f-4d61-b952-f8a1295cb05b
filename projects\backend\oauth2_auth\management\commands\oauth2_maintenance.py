"""
Django management command for OAuth2 system maintenance
"""

from django.core.management.base import BaseCommand, CommandError
from django.utils import timezone
from datetime import <PERSON><PERSON><PERSON>
from oauth2_auth.token_management import token_manager  # Legacy compatibility wrapper
from oauth2_auth.client_management import oauth2_client_manager
from oauth2_auth.security_logger import security_logger
from oauth2_auth.models import Security<PERSON>vent, DeviceToken
from oauth2_provider.models import AccessToken, RefreshToken, Application
from agritram.logger_utils import (
    log_operation_info,
)


class Command(BaseCommand):
    help = "Perform comprehensive OAuth2 system maintenance"

    def add_arguments(self, parser):
        parser.add_argument(
            "--operation",
            type=str,
            choices=[
                "cleanup",
                "audit",
                "reset-user",
                "revoke-client",
                "device-cleanup",
                "security-check",
                "all",
            ],
            default="all",
            help="Maintenance operation to perform (default: all)",
        )
        parser.add_argument(
            "--user-email", type=str, help="User email for user-specific operations"
        )
        parser.add_argument(
            "--client-id", type=str, help="Client ID for client-specific operations"
        )
        parser.add_argument(
            "--days",
            type=int,
            default=30,
            help="Number of days for cleanup operations (default: 30)",
        )
        parser.add_argument(
            "--dry-run",
            action="store_true",
            help="Show what would be done without making changes",
        )
        parser.add_argument(
            "--force", action="store_true", help="Force operations without confirmation"
        )

    def handle(self, *args, **options):
        operation = options["operation"]
        dry_run = options["dry_run"]
        force = options["force"]

        if dry_run:
            self.stdout.write(
                self.style.WARNING("DRY RUN MODE - No changes will be made")
            )

        self.stdout.write(f"Starting OAuth2 maintenance: {operation}")

        try:
            if operation == "all":
                self._perform_all_maintenance(options)
            elif operation == "cleanup":
                self._perform_cleanup(options)
            elif operation == "audit":
                self._perform_audit(options)
            elif operation == "reset-user":
                self._reset_user(options)
            elif operation == "revoke-client":
                self._revoke_client(options)
            elif operation == "device-cleanup":
                self._device_cleanup(options)
            elif operation == "security-check":
                self._security_check(options)

            self.stdout.write(
                self.style.SUCCESS("OAuth2 maintenance completed successfully!")
            )

        except Exception as e:
            log_operation_info(
                "OAUTH2_MAINTENANCE_ERROR",
                f"OAuth2 maintenance failed: {str(e)}",
                metadata={
                    "command_options": options,
                    "error_message": str(e),
                    "error_type": e.__class__.__name__,
                },
                level="ERROR",
            )
            raise CommandError(f"Maintenance failed: {str(e)}")

    def _perform_all_maintenance(self, options):
        """Perform all maintenance operations"""
        self.stdout.write("Performing comprehensive maintenance...")

        # 1. Token cleanup
        self.stdout.write("\n1. Token Cleanup:")
        self._perform_cleanup(options)

        # 2. Device cleanup
        self.stdout.write("\n2. Device Cleanup:")
        self._device_cleanup(options)

        # 3. Security audit
        self.stdout.write("\n3. Security Audit:")
        self._perform_audit(options)

        # 4. Security check
        self.stdout.write("\n4. Security Check:")
        self._security_check(options)

    def _perform_cleanup(self, options):
        """Perform token and data cleanup"""
        dry_run = options["dry_run"]

        if not dry_run:
            # Clean up expired tokens
            results = token_manager.cleanup_expired_tokens()

            if "error" not in results:
                total_cleaned = (
                    results["access_tokens_deleted"]
                    + results["refresh_tokens_deleted"]
                    + results["grants_deleted"]
                )
                self.stdout.write(f"  ✅ Cleaned up {total_cleaned} expired tokens")
                self.stdout.write(
                    f'    - Access tokens: {results["access_tokens_deleted"]}'
                )
                self.stdout.write(
                    f'    - Refresh tokens: {results["refresh_tokens_deleted"]}'
                )
                self.stdout.write(f'    - Grants: {results["grants_deleted"]}')
            else:
                self.stdout.write(
                    self.style.ERROR(f'  ❌ Token cleanup failed: {results["error"]}')
                )
        else:
            # Dry run - show what would be cleaned
            stats = token_manager.get_token_statistics()
            recommendations = stats.get("cleanup_recommendations", {})

            total_would_clean = sum(recommendations.values())
            self.stdout.write(f"  Would clean up {total_would_clean} expired tokens")
            self.stdout.write(
                f'    - Access tokens: {recommendations.get("expired_access_tokens", 0)}'
            )
            self.stdout.write(
                f'    - Refresh tokens: {recommendations.get("expired_refresh_tokens", 0)}'
            )
            self.stdout.write(
                f'    - Grants: {recommendations.get("expired_grants", 0)}'
            )

    def _perform_audit(self, options):
        """Perform security audit"""
        days = options["days"]

        summary = security_logger.get_security_summary(days=days)

        if "error" not in summary:
            self.stdout.write(f"  Security Summary (last {days} days):")
            self.stdout.write(f'    - Total events: {summary.get("total_events", 0)}')
            self.stdout.write(f'    - Unique users: {summary.get("unique_users", 0)}')
            self.stdout.write(f'    - Unique IPs: {summary.get("unique_ips", 0)}')
            self.stdout.write(
                f'    - High risk events: {summary.get("high_risk_events", 0)}'
            )
            self.stdout.write(
                f'    - Critical events: {summary.get("critical_events", 0)}'
            )
            self.stdout.write(f'    - Failed logins: {summary.get("failed_logins", 0)}')
            self.stdout.write(
                f'    - Successful logins: {summary.get("successful_logins", 0)}'
            )

            # Highlight concerning metrics
            if summary.get("high_risk_events", 0) > 10:
                self.stdout.write(
                    self.style.WARNING(f"    ⚠️  High number of risk events detected")
                )

            if summary.get("failed_logins", 0) > 100:
                self.stdout.write(
                    self.style.WARNING(f"    ⚠️  High number of failed logins detected")
                )
        else:
            self.stdout.write(
                self.style.ERROR(f'  ❌ Audit failed: {summary["error"]}')
            )

    def _reset_user(self, options):
        """Reset all tokens and devices for a specific user"""
        user_email = options.get("user_email")
        dry_run = options["dry_run"]
        force = options["force"]

        if not user_email:
            raise CommandError("--user-email is required for reset-user operation")

        try:
            from django.contrib.auth import get_user_model

            User = get_user_model()
            user = User.objects.get(email=user_email)

            if not force and not dry_run:
                confirm = input(
                    f"Are you sure you want to reset all tokens and devices for {user_email}? (yes/no): "
                )
                if confirm.lower() != "yes":
                    self.stdout.write("Operation cancelled")
                    return

            if not dry_run:
                # Revoke all user tokens
                success = token_manager.revoke_user_tokens(user, "Admin maintenance")

                if success:
                    self.stdout.write(f"  ✅ Revoked all tokens for {user_email}")
                else:
                    self.stdout.write(
                        self.style.ERROR(
                            f"  ❌ Failed to revoke tokens for {user_email}"
                        )
                    )

                # Reset device trust
                devices = DeviceToken.objects.filter(user=user)
                device_count = devices.count()
                devices.update(is_trusted=False, trust_expires=None)

                self.stdout.write(f"  ✅ Reset trust for {device_count} devices")
            else:
                # Dry run - show what would be reset
                token_count = AccessToken.objects.filter(user=user).count()
                device_count = DeviceToken.objects.filter(user=user).count()

                self.stdout.write(
                    f"  Would revoke {token_count} tokens for {user_email}"
                )
                self.stdout.write(f"  Would reset trust for {device_count} devices")

        except User.DoesNotExist:
            raise CommandError(f"User {user_email} not found")

    def _revoke_client(self, options):
        """Revoke a specific OAuth2 client"""
        client_id = options.get("client_id")
        dry_run = options["dry_run"]
        force = options["force"]

        if not client_id:
            raise CommandError("--client-id is required for revoke-client operation")

        try:
            application = Application.objects.get(client_id=client_id)

            if not force and not dry_run:
                confirm = input(
                    f"Are you sure you want to revoke client {client_id}? (yes/no): "
                )
                if confirm.lower() != "yes":
                    self.stdout.write("Operation cancelled")
                    return

            if not dry_run:
                success = oauth2_client_manager.revoke_client(
                    application, "Admin maintenance"
                )

                if success:
                    self.stdout.write(f"  ✅ Revoked client {client_id}")
                else:
                    self.stdout.write(
                        self.style.ERROR(f"  ❌ Failed to revoke client {client_id}")
                    )
            else:
                # Dry run - show what would be revoked
                token_count = AccessToken.objects.filter(
                    application=application
                ).count()
                self.stdout.write(
                    f"  Would revoke client {client_id} and {token_count} associated tokens"
                )

        except Application.DoesNotExist:
            raise CommandError(f"Client {client_id} not found")

    def _device_cleanup(self, options):
        """Clean up old and untrusted devices"""
        days = options["days"]
        dry_run = options["dry_run"]

        cutoff_date = timezone.now() - timedelta(days=days)

        # Find old untrusted devices
        old_devices = DeviceToken.objects.filter(
            is_trusted=False, last_seen__lt=cutoff_date
        )

        # Find expired trust devices
        expired_trust = DeviceToken.objects.filter(
            is_trusted=True, trust_expires__lt=timezone.now()
        )

        if not dry_run:
            old_count = old_devices.count()
            old_devices.delete()

            expired_count = expired_trust.count()
            expired_trust.update(is_trusted=False, trust_expires=None)

            self.stdout.write(f"  ✅ Removed {old_count} old untrusted devices")
            self.stdout.write(f"  ✅ Reset trust for {expired_count} expired devices")
        else:
            self.stdout.write(
                f"  Would remove {old_devices.count()} old untrusted devices"
            )
            self.stdout.write(
                f"  Would reset trust for {expired_trust.count()} expired devices"
            )

    def _security_check(self, options):
        """Perform security checks and identify issues"""
        self.stdout.write("  Performing security checks...")

        # Check for suspicious patterns
        one_hour_ago = timezone.now() - timedelta(hours=1)

        # Multiple failed logins
        failed_logins = SecurityEvent.objects.filter(
            event_type="failed_login", created_at__gte=one_hour_ago
        )

        ip_failures = {}
        for event in failed_logins:
            if event.ip_address:
                ip_failures[event.ip_address] = ip_failures.get(event.ip_address, 0) + 1

        suspicious_ips = [ip for ip, count in ip_failures.items() if count >= 5]

        if suspicious_ips:
            self.stdout.write(
                self.style.WARNING(
                    f"    ⚠️  Suspicious IPs detected: {len(suspicious_ips)}"
                )
            )
            for ip in suspicious_ips[:5]:  # Show first 5
                self.stdout.write(f"      - {ip}: {ip_failures[ip]} failed attempts")
        else:
            self.stdout.write("    ✅ No suspicious IP activity detected")

        # Check for blocked devices
        blocked_devices = DeviceToken.objects.filter(is_blocked=True).count()
        if blocked_devices > 0:
            self.stdout.write(
                self.style.WARNING(
                    f"    ⚠️  {blocked_devices} devices currently blocked"
                )
            )
        else:
            self.stdout.write("    ✅ No blocked devices")

        # Check token health
        stats = token_manager.get_token_statistics()
        access_stats = stats.get("access_tokens", {})

        if access_stats.get("total", 0) > 0:
            expired_ratio = access_stats.get("expired", 0) / access_stats.get(
                "total", 1
            )
            if expired_ratio > 0.5:
                self.stdout.write(
                    self.style.WARNING(
                        f"    ⚠️  High ratio of expired tokens: {expired_ratio:.1%}"
                    )
                )
            else:
                self.stdout.write("    ✅ Token health looks good")
        else:
            self.stdout.write("    ℹ️  No tokens in system")

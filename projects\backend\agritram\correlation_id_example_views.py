"""
Example views demonstrating correlation ID usage.
These are examples you can integrate into your existing views.
"""

from django.http import JsonResponse
from django.views.decorators.http import require_http_methods
from django.views.decorators.csrf import csrf_exempt
import json

from .correlation_id_utils import (
    get_request_correlation_id,
    log_info_with_cid,
    log_warning_with_cid,
    log_error_with_cid,
    log_business_event_with_cid,
    create_correlation_context,
    add_correlation_id_to_response_headers
)


@require_http_methods(["GET"])
def correlation_id_test_view(request):
    """
    Test view to demonstrate correlation ID functionality.
    """
    correlation_id = get_request_correlation_id()
    
    # Log the request
    log_info_with_cid(
        "Test endpoint accessed",
        metadata={
            "endpoint": "/api/test/correlation-id/",
            "method": request.method,
            "user": str(request.user) if request.user.is_authenticated else "anonymous"
        }
    )
    
    response_data = {
        "message": "Correlation ID test successful",
        "correlation_id": correlation_id,
        "timestamp": "2025-01-29T12:00:00Z",
        "status": "success"
    }
    
    response = JsonResponse(response_data)
    return add_correlation_id_to_response_headers(response)


@csrf_exempt
@require_http_methods(["POST"])
def business_operation_example(request):
    """
    Example view showing how to use correlation ID in business operations.
    """
    try:
        # Parse request data
        data = json.loads(request.body)
        operation_type = data.get('operation', 'unknown')
        
        correlation_id = get_request_correlation_id()
        
        # Log the start of business operation
        log_business_event_with_cid(
            event_type="OPERATION_START",
            description=f"Starting {operation_type} operation",
            entity_type="operation",
            entity_id=correlation_id,
            metadata={
                "operation_type": operation_type,
                "request_data": data
            }
        )
        
        # Simulate some business logic
        if operation_type == "user_registration":
            # Example: User registration logic
            log_info_with_cid(
                "Processing user registration",
                metadata={"email": data.get("email", "unknown")}
            )
            
            # Simulate validation
            if not data.get("email"):
                log_warning_with_cid(
                    "User registration failed: missing email",
                    metadata={"validation_error": "email_required"}
                )
                return JsonResponse({
                    "error": "Email is required",
                    "correlation_id": correlation_id
                }, status=400)
            
            # Log successful registration
            log_business_event_with_cid(
                event_type="USER_REGISTERED",
                description="User successfully registered",
                entity_type="user",
                entity_id=data.get("email"),
                metadata={"registration_method": "api"}
            )
            
            result = {"message": "User registered successfully"}
            
        elif operation_type == "transaction":
            # Example: Transaction processing
            amount = data.get("amount", 0)
            
            log_info_with_cid(
                f"Processing transaction of {amount}",
                metadata={"amount": amount, "currency": data.get("currency", "USD")}
            )
            
            # Simulate transaction logic
            if amount <= 0:
                log_error_with_cid(
                    "Transaction failed: invalid amount",
                    metadata={"amount": amount, "error": "invalid_amount"}
                )
                return JsonResponse({
                    "error": "Invalid amount",
                    "correlation_id": correlation_id
                }, status=400)
            
            # Log successful transaction
            log_business_event_with_cid(
                event_type="TRANSACTION_COMPLETED",
                description=f"Transaction of {amount} completed",
                entity_type="transaction",
                entity_id=correlation_id,
                metadata={
                    "amount": amount,
                    "currency": data.get("currency", "USD"),
                    "status": "completed"
                }
            )
            
            result = {"message": "Transaction completed", "amount": amount}
            
        else:
            log_warning_with_cid(
                f"Unknown operation type: {operation_type}",
                metadata={"operation_type": operation_type}
            )
            result = {"message": f"Operation {operation_type} processed"}
        
        # Log operation completion
        log_business_event_with_cid(
            event_type="OPERATION_COMPLETED",
            description=f"Operation {operation_type} completed successfully",
            entity_type="operation",
            entity_id=correlation_id,
            metadata={"operation_type": operation_type, "result": result}
        )
        
        response_data = {
            "status": "success",
            "correlation_id": correlation_id,
            "result": result
        }
        
        response = JsonResponse(response_data)
        return add_correlation_id_to_response_headers(response)
        
    except json.JSONDecodeError:
        log_error_with_cid(
            "Invalid JSON in request body",
            metadata={"content_type": request.content_type}
        )
        return JsonResponse({
            "error": "Invalid JSON",
            "correlation_id": get_request_correlation_id()
        }, status=400)
        
    except Exception as e:
        correlation_id = get_request_correlation_id()
        log_error_with_cid(
            f"Unexpected error in business operation: {str(e)}",
            metadata={"exception_type": type(e).__name__, "operation": operation_type}
        )
        return JsonResponse({
            "error": "Internal server error",
            "correlation_id": correlation_id
        }, status=500)


def external_service_call_example():
    """
    Example function showing how to use correlation ID when calling external services.
    This would typically be called from your views or services.
    """
    correlation_id = get_request_correlation_id()
    
    # When making external API calls, include the correlation ID
    headers = {
        'X-Correlation-ID': correlation_id,
        'Content-Type': 'application/json'
    }
    
    log_info_with_cid(
        "Making external service call",
        metadata={
            "service": "external_api",
            "headers": headers,
            "correlation_id_sent": correlation_id
        }
    )
    
    # Example using httpx (which you already have installed)
    # import httpx
    # response = httpx.post(
    #     "https://api.example.com/endpoint",
    #     headers=headers,
    #     json={"data": "example"}
    # )
    
    # Log the response
    log_info_with_cid(
        "External service call completed",
        metadata={
            "service": "external_api",
            "status": "simulated_success",
            "correlation_id": correlation_id
        }
    )
    
    return {"status": "success", "correlation_id": correlation_id}


def async_task_example():
    """
    Example showing how to propagate correlation ID to async tasks.
    """
    correlation_id = get_request_correlation_id()
    
    # When queuing async tasks (like Celery), pass the correlation ID
    log_info_with_cid(
        "Queuing async task",
        metadata={"task_type": "example_task", "correlation_id": correlation_id}
    )
    
    # Example Celery task call with correlation ID
    # from your_app.tasks import example_task
    # example_task.delay(correlation_id=correlation_id, data={"example": "data"})
    
    # In your Celery task, you would use create_correlation_context:
    # @app.task
    # def example_task(correlation_id=None, data=None):
    #     with create_correlation_context(correlation_id):
    #         log_info_with_cid("Processing async task", metadata=data)
    #         # Your task logic here
    
    return correlation_id

from django.core.management.base import BaseCommand
from django.utils import timezone
from datetime import timedelta
from oauth2_provider.models import Grant, AccessToken, RefreshToken
from oauth2_auth.models import SecurityEvent
from oauth2_auth.token_invalidation_service import token_invalidation_service
from agritram.logger_utils import (
    log_operation_info,
    log_database_operation,
    log_security_event_standardized,
    log_performance_metric,
    create_logging_context,
)

class Command(BaseCommand):
    help = "Clean up expired OAuth2 tokens and grants"

    def add_arguments(self, parser):
        parser.add_argument(
            "--days",
            type=int,
            default=7,
            help="Number of days to keep expired tokens for audit purposes",
        )
        parser.add_argument(
            "--dry-run",
            action="store_true",
            help="Show what would be deleted without actually deleting",
        )
        parser.add_argument(
            "--cleanup-events",
            action="store_true",
            help="Also cleanup old security events (older than 90 days)",
        )

    def handle(self, *args, **options):
        days_to_keep = options["days"]
        dry_run = options["dry_run"]
        cleanup_events = options["cleanup_events"]

        cutoff_date = timezone.now() - timedelta(days=days_to_keep)

        # Log command start
        log_operation_info(
            operation_type="CLEANUP_TOKENS_COMMAND_START",
            message="Starting cleanup tokens management command",
            metadata={
                "days_to_keep": days_to_keep,
                "dry_run": dry_run,
                "cleanup_events": cleanup_events,
                "cutoff_date": cutoff_date.isoformat(),
            },
        )

        self.stdout.write(f"Cleaning up tokens expired before: {cutoff_date}")

        if dry_run:
            self.stdout.write(
                self.style.WARNING("DRY RUN MODE - No actual deletions will occur")
            )

        # Clean up expired access tokens
        expired_access_tokens = AccessToken.objects.filter(expires__lt=cutoff_date)
        access_count = expired_access_tokens.count()

        if access_count > 0:
            self.stdout.write(f"Found {access_count} expired access tokens")
            if not dry_run:
                expired_access_tokens.delete()
                self.stdout.write(
                    self.style.SUCCESS(f"Deleted {access_count} expired access tokens")
                )

                log_database_operation(
                    operation_type="DELETE",
                    table_name="oauth2_provider_accesstoken",
                    operation_result="SUCCESS",
                    metadata={
                        "records_deleted": access_count,
                        "cleanup_type": "expired_access_tokens",
                        "cutoff_date": cutoff_date.isoformat(),
                    },
                )
            else:
                log_operation_info(
                    operation_type="CLEANUP_DRY_RUN",
                    message=f"Would delete {access_count} expired access tokens",
                    metadata={
                        "table": "oauth2_provider_accesstoken",
                        "count": access_count,
                    },
                )

        # Clean up expired refresh tokens (Django OAuth Toolkit doesn't have is_revoked field by default)
        expired_refresh_tokens = RefreshToken.objects.filter(
            access_token__expires__lt=cutoff_date
        )
        refresh_count = expired_refresh_tokens.count()

        if refresh_count > 0:
            self.stdout.write(f"Found {refresh_count} expired refresh tokens")
            if not dry_run:
                expired_refresh_tokens.delete()
                self.stdout.write(
                    self.style.SUCCESS(
                        f"Deleted {refresh_count} expired refresh tokens"
                    )
                )

                log_database_operation(
                    operation_type="DELETE",
                    table_name="oauth2_provider_refreshtoken",
                    operation_result="SUCCESS",
                    metadata={
                        "records_deleted": refresh_count,
                        "cleanup_type": "expired_refresh_tokens",
                        "cutoff_date": cutoff_date.isoformat(),
                    },
                )
            else:
                log_operation_info(
                    operation_type="CLEANUP_DRY_RUN",
                    message=f"Would delete {refresh_count} expired refresh tokens",
                    metadata={
                        "table": "oauth2_provider_refreshtoken",
                        "count": refresh_count,
                    },
                )

        # Clean up expired grants
        expired_grants = Grant.objects.filter(expires__lt=timezone.now())
        grant_count = expired_grants.count()

        if grant_count > 0:
            self.stdout.write(f"Found {grant_count} expired grants")
            if not dry_run:
                expired_grants.delete()
                self.stdout.write(
                    self.style.SUCCESS(f"Deleted {grant_count} expired grants")
                )

                log_database_operation(
                    operation_type="DELETE",
                    table_name="oauth2_provider_grant",
                    operation_result="SUCCESS",
                    metadata={
                        "records_deleted": grant_count,
                        "cleanup_type": "expired_grants",
                        "cutoff_date": timezone.now().isoformat(),
                    },
                )
            else:
                log_operation_info(
                    operation_type="CLEANUP_DRY_RUN",
                    message=f"Would delete {grant_count} expired grants",
                    metadata={"table": "oauth2_provider_grant", "count": grant_count},
                )

        # Clean up old security events if requested
        event_count = 0
        if cleanup_events:
            event_cutoff = timezone.now() - timedelta(days=90)
            old_events = SecurityEvent.objects.filter(created_at__lt=event_cutoff)
            event_count = old_events.count()

            if event_count > 0:
                self.stdout.write(f"Found {event_count} old security events")
                if not dry_run:
                    old_events.delete()
                    self.stdout.write(
                        self.style.SUCCESS(f"Deleted {event_count} old security events")
                    )

                    log_database_operation(
                        operation_type="DELETE",
                        table_name="oauth2_auth_securityevent",
                        operation_result="SUCCESS",
                        metadata={
                            "records_deleted": event_count,
                            "cleanup_type": "old_security_events",
                            "cutoff_date": event_cutoff.isoformat(),
                        },
                    )
                else:
                    log_operation_info(
                        operation_type="CLEANUP_DRY_RUN",
                        message=f"Would delete {event_count} old security events",
                        metadata={
                            "table": "oauth2_auth_securityevent",
                            "count": event_count,
                        },
                    )

        total_cleaned = access_count + refresh_count + grant_count
        if cleanup_events:
            total_cleaned += event_count

        # Log cleanup summary
        if total_cleaned == 0:
            self.stdout.write(self.style.SUCCESS("No expired tokens found to clean up"))
            log_operation_info(
                operation_type="CLEANUP_TOKENS_COMMAND_COMPLETE",
                message="Cleanup tokens command completed - no items to clean",
                metadata={"total_items_found": 0, "dry_run": dry_run},
            )
        else:
            action = "Would delete" if dry_run else "Deleted"
            self.stdout.write(
                self.style.SUCCESS(f"{action} {total_cleaned} total items")
            )

            # Log security event for actual cleanup
            if not dry_run and total_cleaned > 0:
                log_security_event_standardized(
                    event_type="TOKEN_CLEANUP_COMMAND",
                    description=f"Management command cleaned up {total_cleaned} expired items",
                    metadata={
                        "access_tokens_deleted": access_count,
                        "refresh_tokens_deleted": refresh_count,
                        "grants_deleted": grant_count,
                        "security_events_deleted": event_count if cleanup_events else 0,
                        "total_deleted": total_cleaned,
                        "days_to_keep": days_to_keep,
                    },
                    level="INFO",
                )

            log_operation_info(
                operation_type="CLEANUP_TOKENS_COMMAND_COMPLETE",
                message=f"Cleanup tokens command completed - {action.lower()} {total_cleaned} items",
                metadata={
                    "total_items_processed": total_cleaned,
                    "dry_run": dry_run,
                    "access_tokens": access_count,
                    "refresh_tokens": refresh_count,
                    "grants": grant_count,
                    "security_events": event_count if cleanup_events else 0,
                },
            )

"""
Registration Orchestrator Service

This service coordinates all registration-related services to handle the complete
user registration flow in a structured and maintainable way.
"""

from typing import Dict, Any, Optional
from oauth2_auth.utils import get_client_ip, generate_device_fingerprint
from agritram.message_utils import StandardSuccessResponse
from agritram.logger_utils import (
    log_request_info,
    log_response_info,
    log_operation_info,
    log_security_event_standardized,
)
from agritram.exceptions import DuplicateResourceException
from .registration_validation_service import RegistrationValidationService
from .device_management_service import DeviceManagementService
from .user_creation_service import UserCreationService
from .activation_service import ActivationService


class RegistrationOrchestrator:
    """Main orchestrator for the user registration process"""

    def __init__(self):
        self.validation_service = RegistrationValidationService()
        self.device_service = DeviceManagementService()
        self.user_service = UserCreationService()
        self.activation_service = ActivationService()

    def register_user(self, request) -> Dict[str, Any]:
        """
        Orchestrate the complete user registration process

        Args:
            request: HTTP request object

        Returns:
            Dict containing registration response data

        Raises:
            Various exceptions based on validation failures
        """

        # Initialize variables for exception handling
        device_data = {}

        try:
            # Log incoming request
            log_request_info( request, "REGISTRATION_REQUEST")

            # Get client information for security logging
            client_ip = get_client_ip(request)
            user_agent = request.META.get("HTTP_USER_AGENT", "")
            fingerprint = generate_device_fingerprint(request)

            # Step 1: Validate registration request
            log_operation_info(
                "REGISTRATION_VALIDATION_START",
                "Starting registration validation",
                metadata={"client_ip": client_ip, "user_agent": user_agent},
            )

            device_data = self.validation_service.validate_registration_request(
                request
            )

            log_operation_info(
                "REGISTRATION_VALIDATION_COMPLETE",
                "Registration validation completed successfully",
                metadata={
                    "email": device_data["email"],
                    "device_id": device_data["device_id"],
                },
            )

            # Step 2: Create user
            log_operation_info(
                "USER_CREATION_START",
                "Starting user creation",
                metadata={"email": device_data["email"]},
            )

            user_creation_result = self.user_service.handle_user_creation_flow(
                request, device_data, fingerprint
            )

            if not user_creation_result["success"]:
                # Log failed registration attempt
                log_security_event_standardized(
                    "FAILED_REGISTRATION",
                    "Registration failed due to validation errors",
                    request=request,
                    metadata={
                        "email": device_data["email"],
                        "errors": user_creation_result["serializer"].errors,
                        "device_name": device_data["device_name"],
                    },
                    level="WARNING",
                )

                return user_creation_result["error_response"]

            user = user_creation_result["user"]
            oauth2_application = user_creation_result["oauth2_application"]
            oauth2_app_created = user_creation_result["oauth2_app_created"]

            log_operation_info(
                "USER_CREATION_COMPLETE",
                f"User creation completed successfully for {user.email}",
                metadata={"user_id": user.id, "oauth2_app_created": oauth2_app_created},
            )

            # Step 3: Handle device registration
            log_operation_info(
                "DEVICE_REGISTRATION_START",
                "Starting device registration",
                metadata={"user_id": user.id, "device_id": device_data["device_id"]},
            )

            device_result = self.device_service.handle_device_registration_flow(
                user, device_data, request
            )

            device_registered = device_result["device_registered"]

            log_operation_info(
                "DEVICE_REGISTRATION_COMPLETE",
                f"Device registration completed for {user.email}",
                metadata={
                    "user_id": user.id,
                    "device_registered": device_registered,
                    "device_id": device_data["device_id"],
                },
            )

            # Step 4: Handle activation flow
            log_operation_info(
                "ACTIVATION_FLOW_START",
                "Starting activation flow",
                metadata={"user_id": user.id},
            )

            activation_result = self.activation_service.handle_activation_flow(
                user, request, device_registered
            )

            log_operation_info(
                "ACTIVATION_FLOW_COMPLETE",
                f"Activation flow completed for {user.email}",
                metadata={
                    "user_id": user.id,
                    "email_sent": activation_result["email_sent"],
                },
            )

            # Step 5: Log comprehensive registration event
            self.user_service.log_user_registration_event(
                user,
                device_data,
                oauth2_app_created,
                device_registered,
                fingerprint,
            )

            # Step 6: Prepare response data
            response_data = self._prepare_response_data(
                user_creation_result, device_result, oauth2_application
            )

            # Log successful registration response
            log_response_info(
                {
                    "message": "Registration successful",
                    "user_id": user.id,
                    "user_email": user.email,
                    "device_registered": device_registered,
                },
                status_code=201,
                operation_type="REGISTRATION_SUCCESS",
            )

            return StandardSuccessResponse.registration_success(
                message="Registration successful",
                details="Account created and activation email sent to your email address",
                user_data=response_data,
            )

        except DuplicateResourceException:
            # Log duplicate registration attempt
            email = device_data.get("email", "unknown") if device_data else "unknown"
            log_operation_info(
                "DUPLICATE_REGISTRATION",
                "Registration attempt with existing email",
                metadata={"email": email},
                level="WARNING",
            )
            # Re-raise our custom exceptions to be handled by the exception handler
            raise

        except Exception as e:
            # Log unexpected registration error
            email = device_data.get("email", "unknown") if device_data else "unknown"
            log_operation_info(
                "REGISTRATION_ERROR",
                f"Unexpected registration error: {str(e)}",
                metadata={
                    "error": str(e),
                    "error_type": e.__class__.__name__,
                    "email": email,
                },
                level="ERROR",
            )
            raise

    def _prepare_response_data(
        self,
        user_creation_result: Dict[str, Any],
        device_result: Dict[str, Any],
        oauth2_application,
    ) -> Dict[str, Any]:
        """
        Prepare response data for successful registration

        Args:
            user_creation_result: Result from user creation flow
            device_result: Result from device registration flow
            oauth2_application: OAuth2 application instance
            unique_id: Unique request ID for correlation

        Returns:
            Dict containing response data
        """
        user_data = {
            "user": user_creation_result["serializer"].data,
            "activation_required": True,
            "device_registered": device_result["device_registered"],
            "device_id": device_result["device_id"],
            "device_name": device_result["device_name"],
        }

        # Include OAuth2 client info if application was created
        if oauth2_application:
            user_data["oauth2_client_id"] = oauth2_application.client_id
            user_data["oauth2_redirect_uri"] = oauth2_application.redirect_uris

        return user_data

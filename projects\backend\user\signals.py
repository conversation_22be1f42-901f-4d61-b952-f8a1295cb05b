from django.db.models.signals import pre_save, post_save
from django.dispatch import receiver
from django.contrib.auth import get_user_model
from django.utils import timezone

# Import standardized logging utilities
from agritram.logger_utils import (
    log_operation_info,
    log_security_event_standardized,
    log_business_event,
    log_database_operation,
)

User = get_user_model()


@receiver(pre_save, sender=User)
def track_user_status_changes(sender, instance, **kwargs):
    """
    Track changes to user status fields and log them for audit purposes.
    """
    if instance.pk:  # Only for existing users (updates)
        try:
            # Get the current state from database
            old_instance = User.objects.get(pk=instance.pk)

            # Track status field changes
            status_fields = [
                "is_active",
                "is_deleted",
                "is_mail_verified",
                "is_permanently_locked",
            ]

            changes = {}
            for field in status_fields:
                old_value = getattr(old_instance, field)
                new_value = getattr(instance, field)

                if old_value != new_value:
                    changes[field] = {
                        "old_value": old_value,
                        "new_value": new_value,
                        "changed_at": timezone.now(),
                    }

            # Log significant status changes
            if changes:
                # Store changes in instance for post_save signal
                instance._status_changes = changes

                # Log critical changes immediately
                critical_changes = []
                if "is_deleted" in changes and changes["is_deleted"]["new_value"]:
                    critical_changes.append("DELETED")
                if "is_active" in changes and not changes["is_active"]["new_value"]:
                    critical_changes.append("DEACTIVATED")
                if (
                    "is_permanently_locked" in changes
                    and changes["is_permanently_locked"]["new_value"]
                ):
                    critical_changes.append("PERMANENTLY_LOCKED")

                if critical_changes:
                    # Log critical security event
                    log_security_event_standardized(
                        "CRITICAL_USER_STATUS_CHANGE",
                        f"Critical user status change for {instance.email}: {', '.join(critical_changes)}",
                        user=instance,
                        metadata={
                            "critical_changes": critical_changes,
                            "changes": changes,
                            "user_id": instance.id,
                            "user_email": instance.email,
                        },
                        level="WARNING",
                    )
                else:
                    # Log regular status change
                    log_operation_info(
                        "USER_STATUS_CHANGE",
                        f"User status change for {instance.email}",
                        metadata={
                            "changes": changes,
                            "user_id": instance.id,
                            "user_email": instance.email,
                        },
                    )

        except User.DoesNotExist:
            # This shouldn't happen, but handle gracefully
            log_operation_info(
                "USER_STATUS_TRACKING_ERROR",
                f"Could not find existing user with pk {instance.pk} for status tracking",
                metadata={
                    "user_pk": instance.pk,
                    "user_email": getattr(instance, "email", "unknown"),
                },
                level="ERROR",
            )
        except Exception as e:
            log_operation_info(
                "USER_STATUS_TRACKING_ERROR",
                f"Error tracking user status changes for {instance.email}: {str(e)}",
                metadata={
                    "error": str(e),
                    "user_id": getattr(instance, "id", None),
                    "user_email": getattr(instance, "email", "unknown"),
                },
                level="ERROR",
            )


@receiver(post_save, sender=User)
def log_user_status_changes(sender, instance, created, **kwargs):
    """
    Log user status changes after save for comprehensive audit trail.
    """
    try:
        if created:
            # Log new user creation as business event
            log_business_event(
                "USER_CREATED",
                f"New user created: {instance.email}",
                entity_type="USER",
                entity_id=str(instance.id),
                metadata={
                    "user_id": instance.id,
                    "user_email": instance.email,
                    "user_role": instance.role,
                    "is_active": instance.is_active,
                    "is_mail_verified": instance.is_mail_verified,
                    "is_deleted": instance.is_deleted,
                    "is_permanently_locked": instance.is_permanently_locked,
                },
            )

            # Log database operation
            log_database_operation(
                "CREATE",
                "User",
                operation_result="SUCCESS",
                metadata={
                    "user_id": instance.id,
                    "user_email": instance.email,
                    "user_role": instance.role,
                },
            )

            # Log if user is created in a restricted state
            restricted_states = []
            if not instance.is_active:
                restricted_states.append("INACTIVE")
            if instance.is_deleted:
                restricted_states.append("DELETED")

            if restricted_states:
                log_security_event_standardized(
                    "USER_CREATED_WITH_RESTRICTIONS",
                    f"User {instance.email} created with restricted status: {', '.join(restricted_states)}",
                    user=instance,
                    metadata={
                        "restricted_states": restricted_states,
                        "user_id": instance.id,
                        "user_email": instance.email,
                    },
                    level="WARNING",
                )
        else:
            # Log status changes for existing users
            if hasattr(instance, "_status_changes"):
                changes = instance._status_changes

                # Create detailed audit log entry
                audit_entry = {
                    "user_id": instance.id,
                    "user_email": instance.email,
                    "timestamp": timezone.now().isoformat(),
                    "changes": changes,
                    "current_status": {
                        "is_active": instance.is_active,
                        "is_deleted": instance.is_deleted,
                        "is_mail_verified": instance.is_mail_verified,
                        "is_permanently_locked": instance.is_permanently_locked,
                    },
                }

                # Log as business event for audit trail
                log_business_event( 
                    "USER_STATUS_UPDATED",
                    f"User status updated for {instance.email}",
                    entity_type="USER",
                    entity_id=str(instance.id),
                    metadata=audit_entry,
                )

                # Log database operation
                log_database_operation(
                    "UPDATE",
                    "User",
                    operation_result="SUCCESS",
                    metadata={
                        "user_id": instance.id,
                        "user_email": instance.email,
                        "changes": changes,
                    },
                )

                # Clean up the temporary attributes
                delattr(instance, "_status_changes")
                if hasattr(instance, "_logging_unique_id"):
                    delattr(instance, "_logging_unique_id")

    except Exception as e:
        log_operation_info(
            "POST_SAVE_USER_LOGGING_ERROR",
            f"Error in post_save user status logging: {str(e)}",
            metadata={
                "error": str(e),
                "user_id": getattr(instance, "id", None),
                "user_email": getattr(instance, "email", "unknown"),
                "created": created,
            },
            level="ERROR",
        )


@receiver(pre_save, sender=User)
def validate_user_status_consistency(sender, instance, **kwargs):
    """
    Validate that user status fields are consistent and logical.
    """
    try:
        # Validation rules
        validation_corrections = []

        # Deleted users should not be active
        if instance.is_deleted:
            if instance.is_active:
                instance.is_active = False
                validation_corrections.append("Auto-deactivated deleted user")

        # Log validation corrections
        if validation_corrections:
            log_operation_info(
                "USER_STATUS_VALIDATION_CORRECTIONS",
                f"User status validation corrections for {instance.email}: {', '.join(validation_corrections)}",
                metadata={
                    "user_id": getattr(instance, "id", None),
                    "user_email": instance.email,
                    "corrections": validation_corrections,
                    "current_status": {
                        "is_active": instance.is_active,
                        "is_deleted": instance.is_deleted,
                        "is_mail_verified": instance.is_mail_verified,
                        "is_permanently_locked": instance.is_permanently_locked,
                    },
                },
                level="INFO",
            )

    except Exception as e:
        log_operation_info( 
            "USER_STATUS_VALIDATION_ERROR",
            f"Error in user status validation: {str(e)}",
            metadata={
                "error": str(e),
                "user_id": getattr(instance, "id", None),
                "user_email": getattr(instance, "email", "unknown"),
            },
            level="ERROR",
        )


# TODO:: Note: Online status tracking has been removed from the User model
# This signal handler is no longer needed but kept for reference
# @receiver(post_save, sender=User)
# def track_online_status_changes(sender, instance, created, **kwargs):
#     """
#     Track when users go online/offline for activity monitoring.
#     Note: is_online field has been removed from User model
#     """
#     pass

import logging
from django.contrib.auth.backends import ModelBackend
from django.utils import timezone
from .models import User
from .auth_utils import UserStatusValidator, log_authentication_attempt
from oauth2_auth.utils import get_client_ip

# Import standardized logging utilities
from agritram.logger_utils import (
    log_operation_info,
    log_database_operation,
    log_performance_metric,
    create_logging_context,
)

logger = logging.getLogger(__name__)


class EmailBackend(ModelBackend):
    def authenticate(self, request, email=None, password=None, **kwargs):
        import time

        start_time = time.time()

        # Create logging context
        logging_context = create_logging_context(
            request=request,
            operation_name="email_backend_authentication",
        )

        # Log authentication attempt start
        log_operation_info(
            operation_type="AUTHENTICATION_START",
            message=f"Starting email backend authentication for: {email}",
            metadata={
                "authentication_method": "email_password",
                "attempted_email": email,
                "client_ip": get_client_ip(request) if request else None,
                "user_agent": (
                    request.META.get("HTTP_USER_AGENT", "") if request else None
                ),
            },
            level="INFO",
        )

        try:
            # Log database operation for user lookup
            log_database_operation(
                operation_type="SELECT",
                table_name="User",
                metadata={"lookup_field": "email", "lookup_value": email},
                level="INFO",
            )

            user = User.objects.get(email=email)

            # Log successful user lookup
            log_database_operation(
                operation_type="SELECT",
                table_name="User",
                operation_result="SUCCESS",
                metadata={
                    "lookup_field": "email",
                    "lookup_value": email,
                    "user_id": user.id,
                    "user_found": True,
                },
                level="INFO",
            )

            # Use centralized user status validation
            validation_result = UserStatusValidator.validate_user_status(
                user, log_violations=True
            )

            if not validation_result["is_valid"]:
                # Log failed authentication attempt with specific error code
                log_authentication_attempt(
                    user=user,
                    success=False,
                    method="password",
                    ip_address=get_client_ip(request) if request else None,
                    user_agent=(
                        request.META.get("HTTP_USER_AGENT", "") if request else None
                    ),
                    error_code=validation_result["error_code"],
                    request=request,
                )

                # Log operation failure with detailed information
                auth_time = (time.time() - start_time) * 1000
                log_operation_info(
                    operation_type="AUTHENTICATION_BLOCKED",
                    message=f"Authentication blocked for user {email}: {validation_result['error_message']}",
                    metadata={
                        "user_id": user.id,
                        "user_email": email,
                        "authentication_method": "email_password",
                        "authentication_result": "blocked",
                        "block_reason": "user_status_violation",
                        "error_code": validation_result["error_code"],
                        "error_message": validation_result["error_message"],
                        "authentication_time_ms": auth_time,
                    },
                    level="WARNING",
                )
                return None

            # Verify password
            if user.check_password(password):
                # Log successful authentication
                log_authentication_attempt(
                    user=user,
                    success=True,
                    method="password",
                    ip_address=get_client_ip(request) if request else None,
                    user_agent=(
                        request.META.get("HTTP_USER_AGENT", "") if request else None
                    ),
                    request=request,
                )

                # Log database operation for updating last login
                log_database_operation(
                    operation_type="UPDATE",
                    table_name="User",
                    metadata={
                        "user_id": user.id,
                        "updated_fields": ["last_login"],
                        "operation_reason": "successful_authentication",
                    },
                    level="INFO",
                )

                # Update last login and online status
                user.last_login = timezone.now()
                user.save(update_fields=["last_login"])

                # Log successful database update
                log_database_operation(
                    operation_type="UPDATE",
                    table_name="User",
                    operation_result="SUCCESS",
                    metadata={
                        "user_id": user.id,
                        "updated_fields": ["last_login"],
                        "last_login_timestamp": user.last_login.isoformat(),
                        "operation_reason": "successful_authentication",
                    },
                    level="INFO",
                )

                # Calculate and log performance metrics
                auth_time = (time.time() - start_time) * 1000
                log_performance_metric(
                    metric_name="authentication_time",
                    metric_value=auth_time,
                    unit="ms",
                    operation_type="email_backend_authentication",
                    metadata={
                        "authentication_method": "email_password",
                        "authentication_result": "success",
                        "user_id": user.id,
                        "user_email": email,
                    },
                    level="INFO",
                )

                # Log successful authentication operation
                log_operation_info(
                    operation_type="AUTHENTICATION_SUCCESS",
                    message=f"Successful email backend authentication for user: {email}",
                    metadata={
                        "user_id": user.id,
                        "user_email": email,
                        "authentication_method": "email_password",
                        "authentication_result": "success",
                        "authentication_time_ms": auth_time,
                        "last_login_updated": True,
                    },
                    level="INFO",
                )

                return user
            else:
                # Log failed password authentication
                log_authentication_attempt(
                    user=user,
                    success=False,
                    method="password",
                    ip_address=get_client_ip(request) if request else None,
                    user_agent=(
                        request.META.get("HTTP_USER_AGENT", "") if request else None
                    ),
                    error_code="INVALID_PASSWORD",
                    request=request,
                )

                # Calculate and log performance metrics for failed authentication
                auth_time = (time.time() - start_time) * 1000
                log_performance_metric(
                    metric_name="authentication_time",
                    metric_value=auth_time,
                    unit="ms",
                    operation_type="email_backend_authentication",
                    metadata={
                        "authentication_method": "email_password",
                        "authentication_result": "failed",
                        "failure_reason": "invalid_password",
                        "user_id": user.id,
                        "user_email": email,
                    },
                    level="INFO",
                )

                # Log failed authentication operation
                log_operation_info(
                    operation_type="AUTHENTICATION_FAILED",
                    message=f"Failed password authentication for user: {email}",
                    metadata={
                        "user_id": user.id,
                        "user_email": email,
                        "authentication_method": "email_password",
                        "authentication_result": "failed",
                        "failure_reason": "invalid_password",
                        "error_code": "INVALID_PASSWORD",
                        "authentication_time_ms": auth_time,
                    },
                    level="WARNING",
                )

                return None

        except User.DoesNotExist:
            # Log failed authentication attempt for non-existent user
            log_authentication_attempt(
                user=None,
                success=False,
                method="password",
                ip_address=get_client_ip(request) if request else None,
                user_agent=request.META.get("HTTP_USER_AGENT", "") if request else None,
                error_code="USER_NOT_FOUND",
                additional_info={"attempted_email": email},
            )

            return None

    def get_user(self, user_id):
        try:
            user = User.objects.get(pk=user_id)

            # Use centralized user status validation for session retrieval
            validation_result = UserStatusValidator.validate_user_status(
                user, log_violations=True
            )

            if not validation_result["is_valid"]:
                log_operation_info(
                    operation_type="SESSION_RETRIEVAL_BLOCKED",
                    message=f"Session retrieval blocked for user {user.email}: {validation_result['error_message']}",
                    metadata={"user_id": user.id, "user_email": user.email},
                    level="WARNING",
                )
                return None

            return user
        except User.DoesNotExist:
            return None

"""
Enhanced logging middleware for comprehensive API request/response logging
with unique IDs, file names, and line numbers.
"""

import json
import time
import uuid
import logging
from django.utils.deprecation import MiddlewareMixin
from django.utils import timezone
from .logger_utils import (
    generate_api_call_uuid,
    get_client_ip,
    log_operation_info,
    log_error_with_traceback,
    get_caller_info,
    set_request_context,
    get_current_unique_id,
    clear_request_context,
)

logger = logging.getLogger(__name__)


class EnhancedAPILoggingMiddleware(MiddlewareMixin):
    """
    Enhanced middleware for comprehensive API logging with unique IDs,
    file names, line numbers, and detailed request/response tracking.
    """

    EXCLUDED_PATHS = [
        "/static/",
        "/media/",
        "/health/",
        "/favicon.ico",
        "/admin/jsi18n/",
    ]

    def process_request(self, request):
        """
        Process incoming request and add unique ID for tracking.
        """
        # Skip logging for excluded paths
        if any(request.path.startswith(path) for path in self.EXCLUDED_PATHS):
            return None

        # Generate unique API call UUID and set in thread-local storage
        unique_id = generate_api_call_uuid()
        request.api_call_uuid = unique_id
        request.start_time = time.time()

        # Set request context in thread-local storage
        context = {
            "request_path": request.path,
            "request_method": request.method,
            "client_ip": get_client_ip(request),
        }

        # Add user info if authenticated
        if hasattr(request, "user") and request.user.is_authenticated:
            context.update(
                {
                    "user_id": request.user.id,
                    "user_email": getattr(request.user, "email", ""),
                    "user_role": getattr(request.user, "role", ""),
                }
            )

        set_request_context(unique_id=unique_id, **context)

        # Get caller information
        caller_info = get_caller_info()

        # Prepare request data for logging
        try:
            # Get request body safely
            request_body = None
            if hasattr(request, "body") and request.body:
                try:
                    request_body = json.loads(request.body.decode("utf-8"))
                except (json.JSONDecodeError, UnicodeDecodeError):
                    request_body = request.body.decode("utf-8", errors="ignore")[:500]

            # Get request headers (exclude sensitive ones)
            headers = {}
            for key, value in request.META.items():
                if key.startswith("HTTP_") and key not in [
                    "HTTP_AUTHORIZATION",
                    "HTTP_COOKIE",
                ]:
                    headers[key[5:].replace("_", "-")] = value

            # Prepare metadata
            metadata = {
                "method": request.method,
                "path": request.path,
                "query_params": dict(request.GET),
                "headers": headers,
                "client_ip": get_client_ip(request),
                "user_agent": request.META.get("HTTP_USER_AGENT", ""),
                "content_type": request.META.get("CONTENT_TYPE", ""),
                "content_length": request.META.get("CONTENT_LENGTH", 0),
                "caller": caller_info,
            }

            # Add user info if authenticated
            if hasattr(request, "user") and request.user.is_authenticated:
                metadata["user"] = {
                    "id": request.user.id,
                    "email": getattr(request.user, "email", ""),
                    "role": getattr(request.user, "role", ""),
                }

            # Add request body if present (limit size for logging)
            if request_body:
                if isinstance(request_body, str):
                    metadata["request_body"] = request_body[
                        :1000
                    ]  # Limit to 1000 chars
                else:
                    metadata["request_body"] = request_body

            # Log the incoming request
            log_operation_info(
                unique_id=unique_id,
                operation_type="API_REQUEST",
                message=f"Incoming {request.method} request to {request.path}",
                metadata=metadata,
                level="INFO",
                include_caller_info=False,  # We already have caller info in metadata
            )

        except Exception as e:
            # Log error in request processing
            log_error_with_traceback(
                unique_id=unique_id,
                operation_type="REQUEST_LOGGING_ERROR",
                message="Error logging incoming request",
                exception=e,
                metadata={"path": request.path, "method": request.method},
            )

        return None

    def process_response(self, request, response):
        """
        Process outgoing response and log completion details.
        """
        # Skip logging for excluded paths
        if any(request.path.startswith(path) for path in self.EXCLUDED_PATHS):
            return response

        # Get unique ID from thread-local storage (fallback to request attribute)
        unique_id = get_current_unique_id()
        if not unique_id:
            unique_id = getattr(request, "api_call_uuid", generate_api_call_uuid())
        start_time = getattr(request, "start_time", time.time())
        response_time = (time.time() - start_time) * 1000  # Convert to milliseconds

        try:
            # Get response data safely
            response_data = None
            if hasattr(response, "content") and response.content:
                try:
                    response_data = json.loads(response.content.decode("utf-8"))
                except (json.JSONDecodeError, UnicodeDecodeError):
                    response_data = response.content.decode("utf-8", errors="ignore")[
                        :500
                    ]

            # Prepare metadata
            metadata = {
                "status_code": response.status_code,
                "response_time_ms": round(response_time, 2),
                "content_type": response.get("Content-Type", ""),
                "content_length": (
                    len(response.content) if hasattr(response, "content") else 0
                ),
            }

            # Add response data if present (limit size for logging)
            if response_data:
                if isinstance(response_data, str):
                    metadata["response_body"] = response_data[
                        :1000
                    ]  # Limit to 1000 chars
                else:
                    metadata["response_body"] = response_data

            # Determine log level based on status code
            if response.status_code >= 500:
                level = "ERROR"
            elif response.status_code >= 400:
                level = "WARNING"
            else:
                level = "INFO"

            # Log the response
            log_operation_info(
                unique_id=unique_id,
                operation_type="API_RESPONSE",
                message=f"Response {response.status_code} for {request.method} {request.path} in {response_time:.2f}ms",
                metadata=metadata,
                level=level,
                include_caller_info=False,
            )

        except Exception as e:
            # Log error in response processing
            log_error_with_traceback(
                unique_id=unique_id,
                operation_type="RESPONSE_LOGGING_ERROR",
                message="Error logging outgoing response",
                exception=e,
                metadata={
                    "path": request.path,
                    "status_code": getattr(response, "status_code", "unknown"),
                },
            )

        # Clear request context at the end of request processing
        clear_request_context()

        return response

    def process_exception(self, request, exception):
        """
        Process exceptions and log them with detailed information.
        """
        # Get unique ID from thread-local storage (fallback to request attribute)
        unique_id = get_current_unique_id()
        if not unique_id:
            unique_id = getattr(request, "api_call_uuid", generate_api_call_uuid())
        start_time = getattr(request, "start_time", time.time())
        response_time = (time.time() - start_time) * 1000

        # Log the exception with full details
        log_error_with_traceback(
            unique_id=unique_id,
            operation_type="API_EXCEPTION",
            message=f"Exception in {request.method} {request.path}: {exception.__class__.__name__}",
            exception=exception,
            metadata={
                "path": request.path,
                "method": request.method,
                "response_time_ms": round(response_time, 2),
                "user_id": (
                    getattr(request.user, "id", None)
                    if hasattr(request, "user") and request.user.is_authenticated
                    else None
                ),
            },
        )

        # Don't handle the exception, just log it
        return None


class RequestUUIDMiddleware(MiddlewareMixin):
    """
    Simple middleware to add UUID to all requests for correlation.
    """

    def process_request(self, request):
        """Add unique UUID to request for tracking."""
        if not hasattr(request, "api_call_uuid"):
            request.api_call_uuid = generate_api_call_uuid()
        return None

"""
Device Management Service

This service handles all device-related functionality for user registration including:
- Device ID generation
- Device information extraction
- Device registration
- Device tracking
"""

from typing import Dict, Any, Tuple, Optional
from django.utils import timezone
from oauth2_auth.utils import (
    get_client_ip,
    generate_device_fingerprint,
    get_dynamic_device_info,
)
from oauth2_auth.device_validation_service import device_validation_service
from oauth2_auth.authentication import DeviceAuthenticationService
from agritram.logger_utils import (
    log_operation_info,
)


class DeviceManagementService:
    """Service for handling all device-related operations during registration"""

    @staticmethod
    def generate_device_id() -> str:
        """
        Generate a cryptographically secure device ID

        Returns:
            str: Generated device ID
        """
        import secrets

        return f"{timezone.now().strftime('%Y%m%d%H%M%S')}_{secrets.token_urlsafe(32)}"

    @staticmethod
    def extract_device_info(
        request, context: str = "registration"
    ) -> Dict[str, Any]:
        """
        Extract comprehensive device information from request

        Args:
            request: HTTP request object
            context: Context for device info extraction

        Returns:
            Dict containing device information
        """
        device_info = get_dynamic_device_info(request, context)
        client_ip = get_client_ip(request)
        fingerprint = generate_device_fingerprint(request)
        user_agent = request.META.get("HTTP_USER_AGENT", "")

        return {
            "device_name": device_info["device_name"],
            "device_type": device_info["device_type"],
            "client_ip": client_ip,
            "fingerprint": fingerprint,
            "user_agent": user_agent,
            "full_device_info": device_info,
        }

    @staticmethod
    def register_device(
        user,
        device_id: str,
        device_name: str,
        device_type: str,
        request,
    ) -> Tuple[bool, Optional[str]]:
        """
        Register a device for the user

        Args:
            user: User instance
            device_id: Device identifier
            device_name: Device name
            device_type: Device type
            request: HTTP request object

        Returns:
            Tuple of (success: bool, error_message: Optional[str])
        """
        try:
            DeviceAuthenticationService.register_device(
                user, device_id, device_name, device_type, request
            )

            log_operation_info(
                "DEVICE_REGISTRATION",
                f"Registered device for user {user.email}",
                metadata={
                    "user_email": user.email,
                    "device_id": device_id,
                    "device_name": device_name,
                    "device_type": device_type,
                },
            )

            return True, None

        except Exception as e:
            error_message = str(e)
            log_operation_info(
                "DEVICE_REGISTRATION",
                f"Failed to register device during registration",
                metadata={
                    "user_email": user.email,
                    "device_id": device_id,
                    "device_name": device_name,
                    "device_type": device_type,
                    "error": error_message,
                },
                level="WARNING",
            )

            return False, error_message

    @staticmethod
    def track_registration_device(
        user, device_id: str, request
    ) -> Dict[str, Any]:
        """
        Track device for cross-flow validation

        Args:
            user: User instance
            device_id: Device identifier
            request: HTTP request object

        Returns:
            Dict containing tracking information
        """
        try:
            tracking_info = device_validation_service.track_registration_device(
                user=user, device_id=device_id, request=request
            )

            log_operation_info(
                "DEVICE_TRACKING",
                f"Device tracking initiated for user {user.email}",
                metadata={
                    "user_email": user.email,
                    "device_id": device_id,
                    "tracking_successful": True,
                },
            )

            return tracking_info

        except Exception as e:
            log_operation_info( 
                "DEVICE_TRACKING",
                f"Failed to track device for user {user.email}",
                metadata={
                    "user_email": user.email,
                    "device_id": device_id,
                    "error": str(e),
                    "tracking_successful": False,
                },
                level="WARNING",
            )

            return {}

    @classmethod
    def handle_device_registration_flow(
        cls, user, device_data: Dict[str, Any], request
    ) -> Dict[str, Any]:
        """
        Handle the complete device registration flow

        Args:
            user: User instance
            device_data: Device information dictionary
            request: HTTP request object

        Returns:
            Dict containing device registration results
        """
        device_id = device_data["device_id"]
        device_name = device_data["device_name"]
        device_type = device_data["device_type"]

        # Register the device
        device_registered, registration_error = cls.register_device(
            user, device_id, device_name, device_type, request
        )

        # Track device for cross-flow validation
        tracking_info = cls.track_registration_device(
            user, device_id, request
        )

        return {
            "device_registered": device_registered,
            "registration_error": registration_error,
            "tracking_info": tracking_info,
            "device_id": device_id,
            "device_name": device_name,
            "device_type": device_type,
        }

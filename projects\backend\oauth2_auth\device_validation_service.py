"""
Cross-Device Authentication Validation Service

Provides fintech-grade device consistency validation across registration,
activation, and login flows to prevent cross-device security issues.
"""

from typing import Dict, Any, Optional, Tuple
from django.utils import timezone
from django.contrib.auth import get_user_model
from django.core.cache import cache
from .models import <PERSON><PERSON><PERSON>oken, SecurityEvent
from .utils import log_security_event, get_client_ip, generate_device_fingerprint
from .config import oauth2_security_config
from agritram.logger_utils import (
    log_operation_info,
    create_logging_context,
)

User = get_user_model()


class DeviceValidationService:
    """
    Service for validating device consistency across authentication flows
    """

    # Use centralized configuration - class-level access
    REGISTRATION_DEVICE_KEY = oauth2_security_config.DEVICE_CACHE_KEYS["registration"]
    ACTIVATION_DEVICE_KEY = oauth2_security_config.DEVICE_CACHE_KEYS["activation"]
    LOGIN_DEVICE_KEY = oauth2_security_config.DEVICE_CACHE_KEYS["login"]
    MAX_DEVICE_CHANGES = oauth2_security_config.MAX_DEVICE_CHANGES
    DEVICE_FLOW_TIMEOUT = oauth2_security_config.DEVICE_FLOW_TIMEOUT
    DEVICE_VALIDATION_MIN_SCORE = oauth2_security_config.DEVICE_VALIDATION_MIN_SCORE

    @classmethod
    def track_registration_device(
        cls, user, device_id, request
    ) -> Dict[str, Any]:
        """
        Track device information during registration

        Args:
            user: User instance
            device_id: Device identifier
            request: HTTP request object
            unique_id: Unique request ID for correlation (optional)

        Returns:
            dict: Device tracking information
        """
        try:
            # Create logging context
            logging_context = create_logging_context(
                request, user, "track_registration_device"
            )

            log_operation_info(
                "REGISTRATION_DEVICE_TRACKING_START",
                f"Starting device tracking for registration - user {user.email}",
                metadata={
                    **logging_context["metadata"],
                    "device_id": device_id,
                    "operation_type": "registration_device_tracking",
                },
            )

            device_info = cls._extract_device_info(device_id, request)

            # Store device info in cache for cross-flow validation
            cache_key = cls.REGISTRATION_DEVICE_KEY.format(user_id=user.id)
            cache.set(cache_key, device_info, cls.DEVICE_FLOW_TIMEOUT)

            # Log device tracking with traditional security event
            log_security_event(
                user=user,
                event_type="registration_device_tracked",
                description="Device information tracked during registration",
                ip_address=device_info["ip_address"],
                user_agent=device_info["user_agent"],
                device_id=device_id,
                metadata={
                    "device_fingerprint": device_info["fingerprint"],
                    "tracking_started": timezone.now().isoformat(),
                },
            )

            # Also log with standardized logging
            log_operation_info(
                "REGISTRATION_DEVICE_TRACKED",
                "Device information tracked during registration",
                metadata={
                    **logging_context["metadata"],
                    "device_id": device_id,
                    "device_fingerprint": device_info["fingerprint"][:20] + "...",
                    "cache_key": cache_key,
                    "cache_timeout": cls.DEVICE_FLOW_TIMEOUT,
                    "security_event": "registration_device_tracked",
                },
            )

            return device_info

        except Exception as e:
            # Also log with standardized logging
            log_operation_info(
                "REGISTRATION_DEVICE_TRACKING_ERROR",
                f"Error tracking registration device: {str(e)}",
                metadata={
                    "user_id": user.id,
                    "user_email": user.email,
                    "device_id": device_id,
                    "client_ip": get_client_ip(request),
                    "error_message": str(e),
                    "error_type": e.__class__.__name__,
                },
                level="ERROR",
            )
            return {}

    @classmethod
    def validate_activation_device(
        cls, user, device_id, request
    ) -> Tuple[bool, str, Dict[str, Any]]:
        """
        Validate device consistency during account activation

        Args:
            user: User instance
            device_id: Device identifier
            request: HTTP request object
            unique_id: Unique request ID for correlation (optional)

        Returns:
            Tuple of (is_valid, message, validation_details)
        """
        try:
            # Create logging context
            logging_context = create_logging_context(
                request, user, "validate_activation_device"
            )

            log_operation_info(
                "ACTIVATION_DEVICE_VALIDATION_START",
                f"Starting device validation for activation - user {user.email}",
                metadata={
                    **logging_context["metadata"],
                    "device_id": device_id,
                    "operation_type": "activation_device_validation",
                },
            )

            current_device_info = cls._extract_device_info(
                device_id, request
            )

            # Get registration device info
            reg_cache_key = cls.REGISTRATION_DEVICE_KEY.format(user_id=user.id)
            registration_device = cache.get(reg_cache_key)

            validation_result = cls._validate_device_consistency(
                    registration_device, current_device_info, "activation"
            )

            # Store activation device info
            act_cache_key = cls.ACTIVATION_DEVICE_KEY.format(user_id=user.id)
            cache.set(act_cache_key, current_device_info, cls.DEVICE_FLOW_TIMEOUT)

            # Log validation result with traditional security event
            log_security_event(
                user=user,
                event_type="activation_device_validated",
                description=f'Device validation during activation: {"passed" if validation_result["is_valid"] else "failed"}',
                ip_address=current_device_info["ip_address"],
                user_agent=current_device_info["user_agent"],
                device_id=device_id,
                metadata={
                    "validation_result": validation_result,
                    "security_score": validation_result.get("security_score", 0),
                },
            )

            # Also log with standardized logging
            log_operation_info(
                "ACTIVATION_DEVICE_VALIDATED",
                f'Device validation during activation: {"passed" if validation_result["is_valid"] else "failed"}',
                metadata={
                    **logging_context["metadata"],
                    "device_id": device_id,
                    "validation_passed": validation_result["is_valid"],
                    "security_score": validation_result.get("security_score", 0),
                    "warnings": validation_result.get("warnings", []),
                    "cache_key": act_cache_key,
                    "security_event": "activation_device_validated",
                },
                level="WARNING" if not validation_result["is_valid"] else "INFO",
            )

            return (
                validation_result["is_valid"],
                validation_result["message"],
                validation_result,
            )

        except Exception as e:
            # Also log with standardized logging
            log_operation_info(
                "ACTIVATION_DEVICE_VALIDATION_ERROR",
                f"Error validating activation device: {str(e)}",
                metadata={
                    "user_id": user.id,
                    "user_email": user.email,
                    "device_id": device_id,
                    "client_ip": get_client_ip(request),
                    "error_message": str(e),
                    "error_type": e.__class__.__name__,
                },
                level="ERROR",
            )
            return False, "Device validation failed", {"error": str(e)}

    @classmethod
    def validate_login_device(
        cls, user, device_id, request
    ) -> Tuple[bool, str, Dict[str, Any]]:
        """
        Validate device consistency during login

        Args:
            user: User instance
            device_id: Device identifier
            request: HTTP request object
            unique_id: Unique request ID for correlation (optional)

        Returns:
            Tuple of (is_valid, message, validation_details)
        """
        try:
            # Create logging context
            logging_context = create_logging_context(
                request, user, "validate_login_device"
            )

            log_operation_info(
                "LOGIN_DEVICE_VALIDATION_START",
                f"Starting device validation for login - user {user.email}",
                metadata={
                    **logging_context["metadata"],
                    "device_id": device_id,
                    "operation_type": "login_device_validation",
                },
            )

            current_device_info = cls._extract_device_info(
                device_id, request
            )

            # Get previous device info (prefer activation, fallback to registration)
            act_cache_key = cls.ACTIVATION_DEVICE_KEY.format(user_id=user.id)
            reg_cache_key = cls.REGISTRATION_DEVICE_KEY.format(user_id=user.id)

            previous_device = cache.get(act_cache_key) or cache.get(reg_cache_key)

            validation_result = cls._validate_device_consistency(
                previous_device, current_device_info, "login"
            )

            # Store login device info
            login_cache_key = cls.LOGIN_DEVICE_KEY.format(user_id=user.id)
            cache.set(login_cache_key, current_device_info, cls.DEVICE_FLOW_TIMEOUT)

            # Log validation result with traditional security event
            log_security_event(
                user=user,
                event_type="login_device_validated",
                description=f'Device validation during login: {"passed" if validation_result["is_valid"] else "failed"}',
                ip_address=current_device_info["ip_address"],
                user_agent=current_device_info["user_agent"],
                device_id=device_id,
                metadata={
                    "validation_result": validation_result,
                    "security_score": validation_result.get("security_score", 0),
                },
            )

            # Also log with standardized logging
            log_operation_info(
                "LOGIN_DEVICE_VALIDATED",
                f'Device validation during login: {"passed" if validation_result["is_valid"] else "failed"}',
                metadata={
                    **logging_context["metadata"],
                    "device_id": device_id,
                    "validation_passed": validation_result["is_valid"],
                    "security_score": validation_result.get("security_score", 0),
                    "warnings": validation_result.get("warnings", []),
                    "cache_key": login_cache_key,
                    "security_event": "login_device_validated",
                },
                level="WARNING" if not validation_result["is_valid"] else "INFO",
            )

            return (
                validation_result["is_valid"],
                validation_result["message"],
                validation_result,
            )

        except Exception as e:
            # Also log with standardized logging
            log_operation_info(
                "LOGIN_DEVICE_VALIDATION_ERROR",
                f"Error validating login device: {str(e)}",
                metadata={
                    "user_id": user.id,
                    "user_email": user.email,
                    "device_id": device_id,
                    "client_ip": get_client_ip(request),
                    "error_message": str(e),
                    "error_type": e.__class__.__name__,
                },
                level="ERROR",
            )
            return False, "Device validation failed", {"error": str(e)}

    @classmethod
    def _extract_device_info(
        cls, device_id, request
    ) -> Dict[str, Any]:
        """Extract device information from request"""
        return {
            "device_id": device_id,
            "ip_address": get_client_ip(request),
            "user_agent": request.META.get("HTTP_USER_AGENT", ""),
            "fingerprint": generate_device_fingerprint(request),
            "timestamp": timezone.now().isoformat(),
        }

    @classmethod
    def _validate_device_consistency(
        cls, previous_device, current_device, flow_type
    ) -> Dict[str, Any]:
        """
        Validate device consistency between flows

        Args:
            previous_device: Previous device information
            current_device: Current device information
            flow_type: Type of flow ('activation', 'login')
            unique_id: Unique request ID for correlation (optional)

        Returns:
            dict: Validation result with security scoring
        """
        if not previous_device:
            # No previous device to compare - allow but with lower security score
            log_operation_info(
                "DEVICE_CONSISTENCY_NO_PREVIOUS",
                f"No previous device information available for {flow_type}",
                metadata={
                    "flow_type": flow_type,
                    "current_device_id": current_device.get("device_id"),
                    "security_score": 50,
                    "validation_result": "allowed_with_warning",
                },
                level="WARNING",
            )
            return {
                "is_valid": True,
                "message": "No previous device information available",
                "security_score": 50,  # Medium security score
                "warnings": ["no_previous_device_data"],
            }

        security_score = 100
        warnings = []

        # Check device ID consistency
        if previous_device.get("device_id") != current_device.get("device_id"):
            security_score -= 30
            warnings.append("device_id_mismatch")

        # Check IP address consistency
        if previous_device.get("ip_address") != current_device.get("ip_address"):
            security_score -= 20
            warnings.append("ip_address_change")

        # Check device fingerprint consistency
        if previous_device.get("fingerprint") != current_device.get("fingerprint"):
            security_score -= 25
            warnings.append("device_fingerprint_change")

        # Check user agent consistency (less critical)
        if previous_device.get("user_agent") != current_device.get("user_agent"):
            security_score -= 10
            warnings.append("user_agent_change")

        # Determine if validation passes based on security score
        is_valid = security_score >= cls.DEVICE_VALIDATION_MIN_SCORE

        if not is_valid:
            message = f"Device validation failed for {flow_type}: security score {security_score}%"
        else:
            message = f"Device validation passed for {flow_type}: security score {security_score}%"

        # Log device consistency validation result
        log_operation_info(
            "DEVICE_CONSISTENCY_VALIDATION",
            message,
            metadata={
                "flow_type": flow_type,
                "validation_passed": is_valid,
                "security_score": security_score,
                "warnings": warnings,
                "min_required_score": cls.DEVICE_VALIDATION_MIN_SCORE,
                "previous_device_id": previous_device.get("device_id"),
                "current_device_id": current_device.get("device_id"),
                "previous_ip": previous_device.get("ip_address"),
                "current_ip": current_device.get("ip_address"),
            },
            level="WARNING" if not is_valid else "INFO",
        )

        return {
            "is_valid": is_valid,
            "message": message,
            "security_score": security_score,
            "warnings": warnings,
            "flow_type": flow_type,
            "comparison": {"previous": previous_device, "current": current_device},
        }

    @classmethod
    def cleanup_device_tracking(cls, user_id):
        """Clean up device tracking cache for a user"""
        try:
            cache_keys = [
                cls.REGISTRATION_DEVICE_KEY.format(user_id=user_id),
                cls.ACTIVATION_DEVICE_KEY.format(user_id=user_id),
                cls.LOGIN_DEVICE_KEY.format(user_id=user_id),
            ]

            for key in cache_keys:
                cache.delete(key)

            # Also log with standardized logging
            log_operation_info(
                "DEVICE_TRACKING_CLEANUP",
                f"Cleaned up device tracking for user {user_id}",
                metadata={
                    "user_id": user_id,
                    "cache_keys_cleaned": cache_keys,
                    "operation_type": "device_tracking_cleanup",
                },
            )

        except Exception as e:
            # Also log with standardized logging
            log_operation_info(
                "DEVICE_TRACKING_CLEANUP_ERROR",
                f"Error cleaning up device tracking: {str(e)}",
                metadata={
                    "user_id": user_id,
                    "error_message": str(e),
                    "error_type": e.__class__.__name__,
                },
                level="ERROR",
            )


# Global service instance
device_validation_service = DeviceValidationService()

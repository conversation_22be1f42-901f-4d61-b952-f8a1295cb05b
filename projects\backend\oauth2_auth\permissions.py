from rest_framework import permissions
from rest_framework.exceptions import PermissionDenied
from oauth2_provider.contrib.rest_framework import TokenHasScope
from .models import DeviceToken
from .utils import get_client_ip
from user.permissions import UserStatusMixin
import time
from agritram.logger_utils import (
    log_operation_info,
    log_security_event_standardized,
    log_business_event,
    log_performance_metric,
    create_logging_context,
)


class OAuth2ScopePermission(TokenHasScope):
    """
    Enhanced OAuth2 scope-based permission with logging
    """

    def has_permission(self, request, view):
        start_time = time.time()

        log_operation_info(
            "OAUTH2_SCOPE_PERMISSION_CHECK_START",
            f"Starting OAuth2 scope permission check for view: {view.__class__.__name__}",
            metadata={
                "view_name": view.__class__.__name__,
                "required_scopes": getattr(self, "required_scopes", []),
                "user_authenticated": hasattr(request, "user")
                and request.user.is_authenticated,
                "user_id": (
                    request.user.id
                    if hasattr(request, "user") and request.user.is_authenticated
                    else None
                ),
                "operation_type": "oauth2_scope_permission_check",
            },
        )

        # Call parent implementation
        has_permission = super().has_permission(request, view)

        permission_check_duration = (time.time() - start_time) * 1000

        # Log performance metric
        log_performance_metric(
            "oauth2_scope_permission_check_time",
            permission_check_duration,
            unit="ms",
            operation_type="oauth2_scope_permission_check",
            metadata={
                "view_name": view.__class__.__name__,
                "permission_granted": has_permission,
                "user_id": (
                    request.user.id
                    if hasattr(request, "user") and request.user.is_authenticated
                    else None
                ),
            },
        )

        if not has_permission and hasattr(request, "auth") and request.auth:
            # Log insufficient scope
            token_scopes = (
                set(request.auth.scope.split()) if request.auth.scope else set()
            )
            required_scopes = set(self.required_scopes)

            # Log security event with standardized logging
            log_security_event_standardized(
                "INSUFFICIENT_OAUTH2_SCOPE",
                f"Insufficient OAuth2 scope. Required: {required_scopes}, Available: {token_scopes}",
                user=request.user if request.user.is_authenticated else None,
                request=request,
                metadata={
                    "required_scopes": list(required_scopes),
                    "available_scopes": list(token_scopes),
                    "endpoint": request.path,
                    "view_name": view.__class__.__name__,
                    "permission_check_time_ms": permission_check_duration,
                },
                level="WARNING",
            )

            # Log business event for security violation
            log_business_event(
                "OAUTH2_SCOPE_VIOLATION",
                f"OAuth2 scope violation detected for view: {view.__class__.__name__}",
                entity_type="SECURITY_VIOLATION",
                metadata={
                    "user_id": (
                        request.user.id if request.user.is_authenticated else None
                    ),
                    "user_email": (
                        request.user.email if request.user.is_authenticated else None
                    ),
                    "view_name": view.__class__.__name__,
                    "required_scopes": list(required_scopes),
                    "available_scopes": list(token_scopes),
                    "endpoint": request.path,
                    "violation_type": "insufficient_scope",
                },
                level="WARNING",
            )

        # Log general permission check result
        if hasattr(request, "user") and request.user.is_authenticated:
            log_operation_info(
                "OAUTH2_SCOPE_PERMISSION_CHECK_COMPLETED",
                f"OAuth2 scope permission check completed: {'granted' if has_permission else 'denied'}",
                metadata={
                    "view_name": view.__class__.__name__,
                    "required_scopes": getattr(self, "required_scopes", []),
                    "permission_granted": has_permission,
                    "user_id": request.user.id,
                    "user_email": request.user.email,
                    "permission_check_time_ms": permission_check_duration,
                },
                level="WARNING" if not has_permission else "INFO",
            )
        else:
            log_operation_info(
                "OAUTH2_SCOPE_PERMISSION_CHECK_UNAUTHENTICATED",
                f"OAuth2 scope permission check for unauthenticated user: {has_permission}",
                metadata={
                    "view_name": view.__class__.__name__,
                    "required_scopes": getattr(self, "required_scopes", []),
                    "permission_granted": has_permission,
                    "permission_check_time_ms": permission_check_duration,
                },
                level="WARNING",
            )

        return has_permission


class TrustedDevicePermission(permissions.BasePermission, UserStatusMixin):
    """
    Permission class requiring trusted device
    """

    # TODO: NEED to use something like this please

    def has_permission(self, request, view):
        if not request.user.is_authenticated:
            log_security_event_standardized(
                "UNAUTHENTICATED_TRUSTED_DEVICE_ACCESS",
                "Unauthenticated user attempted to access trusted device endpoint",
                request=request,
                metadata={
                    "view_name": view.__class__.__name__,
                    "endpoint": request.path,
                },
                level="WARNING",
            )
            return False

        # Check user status first
        is_valid, reason = self.check_user_status(request.user)
        if not is_valid:
            log_operation_info(
                "TRUSTED_DEVICE_PERMISSION_USER_STATUS_INVALID",
                f"TrustedDevice permission denied for {request.user.email}: {reason}",
                metadata={
                    "user_id": request.user.id,
                    "user_email": request.user.email,
                    "reason": reason,
                    "view_name": view.__class__.__name__,
                },
                level="WARNING",
            )
            return False

        # Get device ID from OAuth2 token or request headers
        device_id = None
        if hasattr(request, "auth") and hasattr(request.auth, "device_id"):
            device_id = request.auth.device_id
        else:
            device_id = request.META.get("HTTP_X_DEVICE_ID")

        if not device_id:
            log_security_event_standardized(
                "DEVICE_ID_MISSING",
                "Device identification required but not provided",
                user=request.user,
                metadata={
                    "endpoint": request.path,
                    "view_name": view.__class__.__name__,
                },
                level="WARNING",
            )
            return False

        try:
            device = DeviceToken.objects.get(user=request.user, device_id=device_id)
            is_trusted = device.is_trust_valid()

            if not is_trusted:
                log_security_event_standardized(
                    "UNTRUSTED_DEVICE_ACCESS",
                    f"Access attempt from untrusted device: {device.device_name}",
                    user=request.user,
                    request=request,
                    metadata={
                        "device_id": device_id,
                        "device_name": device.device_name,
                        "device_type": device.device_type,
                        "endpoint": request.path,
                        "view_name": view.__class__.__name__,
                    },
                    level="WARNING",
                )

            # Log successful trusted device access
            if is_trusted:
                log_operation_info(
                    "TRUSTED_DEVICE_ACCESS_GRANTED",
                    f"Trusted device access granted for device: {device.device_name}",
                    metadata={
                        "user_id": request.user.id,
                        "user_email": request.user.email,
                        "device_id": device_id,
                        "device_name": device.device_name,
                        "view_name": view.__class__.__name__,
                    },
                )

            return is_trusted

        except DeviceToken.DoesNotExist:
            log_security_event_standardized(
                "UNREGISTERED_DEVICE_ACCESS",
                f"Access attempt from unregistered device: {device_id}",
                user=request.user,
                request=request,
                metadata={
                    "device_id": device_id,
                    "endpoint": request.path,
                    "view_name": view.__class__.__name__,
                },
                level="WARNING",
            )
            return False


class SensitiveOperationPermission(permissions.BasePermission):
    """
    Permission class for sensitive operations requiring multiple security checks
    """

    # TODO: NEED to use something like this please

    def has_permission(self, request, view):
        # Combine multiple permission checks
        permission_classes = [
            OAuth2ScopePermission,
            TrustedDevicePermission,
        ]

        for permission_class in permission_classes:
            permission = permission_class()
            if hasattr(permission, "required_scopes"):
                permission.required_scopes = [
                    "write"
                ]  # Require write scope for sensitive operations

            if not permission.has_permission(request, view):
                log_security_event_standardized(
                    "SENSITIVE_OPERATION_PERMISSION_DENIED",
                    f"Sensitive operation permission denied by {permission_class.__name__}",
                    user=request.user if request.user.is_authenticated else None,
                    request=request,
                    metadata={
                        "failed_permission": permission_class.__name__,
                        "method": request.method,
                        "endpoint": request.path,
                        "view_name": view.__class__.__name__,
                    },
                    level="WARNING",
                )
                return False

        # Log sensitive operation access
        log_security_event_standardized(
            "SENSITIVE_OPERATION_ACCESS",
            f"Sensitive operation accessed: {request.path}",
            user=request.user,
            request=request,
            metadata={
                "method": request.method,
                "endpoint": request.path,
                "view_name": view.__class__.__name__,
            },
            level="INFO",
        )

        return True


class ReadOnlyPermission(OAuth2ScopePermission):
    """Permission for read-only operations"""

    required_scopes = ["read"]


class WritePermission(OAuth2ScopePermission):
    """Permission for write operations"""

    required_scopes = ["write"]


class ProfilePermission(OAuth2ScopePermission):
    """Permission for profile access"""

    required_scopes = ["profile"]


class EmailPermission(OAuth2ScopePermission):
    """Permission for email access"""

    required_scopes = ["email"]


class OpenIDPermission(OAuth2ScopePermission):
    """Permission for OpenID Connect"""

    # TODO: NEED to use something like this please
    required_scopes = ["openid"]


# Role-based permissions
class FarmerPermission(OAuth2ScopePermission, UserStatusMixin):
    """Permission for farmer role operations"""

    # TODO: NEED to use something like this please
    required_scopes = ["farmer"]

    def has_permission(self, request, view):
        # Check OAuth2 scope first
        if not super().has_permission(request, view):
            return False

        # Check user status
        if hasattr(request, "user") and request.user.is_authenticated:
            is_valid, reason = self.check_user_status(request.user)
            if not is_valid:
                log_operation_info( 
                    "FARMER_PERMISSION_USER_STATUS_INVALID",
                    f"Farmer permission denied for {request.user.email}: {reason}",
                    metadata={
                        "user_id": request.user.id,
                        "user_email": request.user.email,
                        "reason": reason,
                        "view_name": view.__class__.__name__,
                    },
                    level="WARNING",
                )
                return False

            has_farmer_role = request.user.role == "farmer"

            # Log role check result
            log_operation_info(
                "FARMER_PERMISSION_ROLE_CHECK",
                f"Farmer role check for {request.user.email}: {'granted' if has_farmer_role else 'denied'}",
                metadata={
                    "user_id": request.user.id,
                    "user_email": request.user.email,
                    "user_role": request.user.role,
                    "permission_granted": has_farmer_role,
                    "view_name": view.__class__.__name__,
                },
                level="WARNING" if not has_farmer_role else "INFO",
            )

            return has_farmer_role

        return False


class TraderPermission(OAuth2ScopePermission, UserStatusMixin):
    """Permission for trader role operations"""

    # TODO: NEED to use something like this please
    required_scopes = ["trader"]

    def has_permission(self, request, view):
        # Check OAuth2 scope first
        if not super().has_permission(request, view):
            return False

        # Check user status and role
        if hasattr(request, "user") and request.user.is_authenticated:
            is_valid, reason = self.check_user_status(request.user)
            if not is_valid:
                log_operation_info(
                    "TRADER_PERMISSION_USER_STATUS_INVALID",
                    f"Trader permission denied for {request.user.email}: {reason}",
                    metadata={
                        "user_id": request.user.id,
                        "user_email": request.user.email,
                        "reason": reason,
                        "view_name": view.__class__.__name__,
                    },
                    level="WARNING",
                )
                return False

            has_trader_role = request.user.role == "trader"

            # Log role check result
            log_operation_info(
                "TRADER_PERMISSION_ROLE_CHECK",
                f"Trader role check for {request.user.email}: {'granted' if has_trader_role else 'denied'}",
                metadata={
                    "user_id": request.user.id,
                    "user_email": request.user.email,
                    "user_role": request.user.role,
                    "permission_granted": has_trader_role,
                    "view_name": view.__class__.__name__,
                },
                level="WARNING" if not has_trader_role else "INFO",
            )

            return has_trader_role

        return False


class ManufacturerPermission(OAuth2ScopePermission, UserStatusMixin):
    """Permission for manufacturer role operations"""

    # TODO: NEED to use something like this please
    required_scopes = ["manufacturer"]

    def has_permission(self, request, view):
        # Check OAuth2 scope first
        if not super().has_permission(request, view):
            return False

        # Check user status and role
        if hasattr(request, "user") and request.user.is_authenticated:
            is_valid, reason = self.check_user_status(request.user)
            if not is_valid:
                log_operation_info(
                    "MANUFACTURER_PERMISSION_USER_STATUS_INVALID",
                    f"Manufacturer permission denied for {request.user.email}: {reason}",
                    metadata={
                        "user_id": request.user.id,
                        "user_email": request.user.email,
                        "reason": reason,
                        "view_name": view.__class__.__name__,
                    },
                    level="WARNING",
                )
                return False

            has_manufacturer_role = request.user.role == "manufacturer"

            # Log role check result
            log_operation_info(
                "MANUFACTURER_PERMISSION_ROLE_CHECK",
                f"Manufacturer role check for {request.user.email}: {'granted' if has_manufacturer_role else 'denied'}",
                metadata={
                    "user_id": request.user.id,
                    "user_email": request.user.email,
                    "user_role": request.user.role,
                    "permission_granted": has_manufacturer_role,
                    "view_name": view.__class__.__name__,
                },
                level="WARNING" if not has_manufacturer_role else "INFO",
            )

            return has_manufacturer_role

        return False


class AdminPermission(OAuth2ScopePermission):
    """Permission for admin role operations"""

    required_scopes = ["admin"]

    def has_permission(self, request, view):
        # Check OAuth2 scope first
        if not super().has_permission(request, view):
            return False

        # Check user role and admin status
        if hasattr(request, "user") and request.user.is_authenticated:
            has_admin_access = (
                request.user.role == "admin"
                or request.user.is_staff
                or request.user.is_superuser
            )

            # Log admin permission check
            log_operation_info(
                "ADMIN_PERMISSION_CHECK",
                f"Admin permission check for {request.user.email}: {'granted' if has_admin_access else 'denied'}",
                metadata={
                    "user_id": request.user.id,
                    "user_email": request.user.email,
                    "user_role": request.user.role,
                    "is_staff": request.user.is_staff,
                    "is_superuser": request.user.is_superuser,
                    "permission_granted": has_admin_access,
                    "view_name": view.__class__.__name__,
                },
                level="WARNING" if not has_admin_access else "INFO",
            )

            return has_admin_access

        return False


class AnyRolePermission(OAuth2ScopePermission):
    """Permission that allows any authenticated user with proper scope"""

    required_scopes = ["read"]

    def has_permission(self, request, view):
        # Check OAuth2 scope first
        if not super().has_permission(request, view):
            return False

        # Allow any authenticated user
        return hasattr(request, "user") and request.user.is_authenticated


class OwnerOrAdminPermission(permissions.BasePermission):
    """
    Permission that allows access to owners of objects or admins
    """

    def has_permission(self, request, view):
        return request.user and request.user.is_authenticated

    def has_object_permission(self, request, view, obj):
        # Admin users can access everything
        is_admin = (
            request.user.is_staff
            or request.user.is_superuser
            or request.user.role == "admin"
        )

        if is_admin:
            log_operation_info( 
                "OWNER_OR_ADMIN_PERMISSION_ADMIN_ACCESS",
                f"Admin access granted to {request.user.email} for object {obj.__class__.__name__}",
                metadata={
                    "user_id": request.user.id,
                    "user_email": request.user.email,
                    "user_role": request.user.role,
                    "is_staff": request.user.is_staff,
                    "is_superuser": request.user.is_superuser,
                    "object_type": obj.__class__.__name__,
                    "object_id": getattr(obj, "id", None),
                    "view_name": view.__class__.__name__,
                },
            )
            return True

        # Check if user owns the object
        is_owner = False
        owner_field = None

        if hasattr(obj, "user"):
            is_owner = obj.user == request.user
            owner_field = "user"
        elif hasattr(obj, "owner"):
            is_owner = obj.owner == request.user
            owner_field = "owner"
        elif hasattr(obj, "created_by"):
            is_owner = obj.created_by == request.user
            owner_field = "created_by"

        # Log ownership check result
        log_operation_info(
            "OWNER_OR_ADMIN_PERMISSION_OWNERSHIP_CHECK",
            f"Ownership check for {request.user.email}: {'granted' if is_owner else 'denied'}",
            metadata={
                "user_id": request.user.id,
                "user_email": request.user.email,
                "object_type": obj.__class__.__name__,
                "object_id": getattr(obj, "id", None),
                "owner_field": owner_field,
                "is_owner": is_owner,
                "view_name": view.__class__.__name__,
            },
            level="WARNING" if not is_owner else "INFO",
        )

        return is_owner


class ReadWritePermission(permissions.BasePermission):
    """
    Permission class that requires different scopes for read vs write operations
    """

    # TODO: NEED to use something like this please

    def has_permission(self, request, view):
        # Determine required scope based on HTTP method
        if request.method in permissions.SAFE_METHODS:
            # Read operations require 'read' scope
            permission = ReadOnlyPermission()
        else:
            # Write operations require 'write' scope
            permission = WritePermission()

        return permission.has_permission(request, view)


class CropManagementPermission(permissions.BasePermission, UserStatusMixin):
    """
    Permission for crop management operations
    Business Rule: Traders can create crops from farmers, farmers/traders/manufacturers can view
    """

    # TODO: NEED to use something like this please

    def has_permission(self, request, view):
        if not (request.user and request.user.is_authenticated):
            log_security_event_standardized(
                "UNAUTHENTICATED_CROP_MANAGEMENT_ACCESS",
                "Unauthenticated user attempted to access crop management endpoint",
                request=request,
                metadata={
                    "view_name": view.__class__.__name__,
                    "endpoint": request.path,
                },
                level="WARNING",
            )
            return False

        # Check user status first
        is_valid, reason = self.check_user_status(request.user)
        if not is_valid:
            log_operation_info(
                "CROP_MANAGEMENT_PERMISSION_USER_STATUS_INVALID",
                f"Crop management permission denied for {request.user.email}: {reason}",
                metadata={
                    "user_id": request.user.id,
                    "user_email": request.user.email,
                    "reason": reason,
                    "view_name": view.__class__.__name__,
                },
                level="WARNING",
            )
            return False

        user_role = request.user.role

        # Read operations - farmers, traders, manufacturers can view crops
        if request.method in permissions.SAFE_METHODS:
            has_read_permission = user_role in [
                "farmer",
                "trader",
                "manufacturer",
                "admin",
            ]
        else:
            # Write operations - farmers can create their own crops, traders can create from farmers
            has_read_permission = user_role in ["farmer", "trader", "admin"]

        # Log permission check result
        log_operation_info(
            "CROP_MANAGEMENT_PERMISSION_CHECK",
            f"Crop management permission for {request.user.email}: {'granted' if has_read_permission else 'denied'}",
            metadata={
                "user_id": request.user.id,
                "user_email": request.user.email,
                "user_role": user_role,
                "request_method": request.method,
                "permission_granted": has_read_permission,
                "view_name": view.__class__.__name__,
            },
            level="WARNING" if not has_read_permission else "INFO",
        )

        return has_read_permission

    def has_object_permission(self, request, view, obj):
        user_role = request.user.role

        # Admin can do everything
        if user_role == "admin" or request.user.is_staff:
            return True

        # Read operations - all authenticated users can read
        if request.method in permissions.SAFE_METHODS:
            return True

        # Write operations
        if user_role == "farmer":
            # Farmers can only modify their own crops
            if hasattr(obj, "farmer"):
                return obj.farmer == request.user
            elif hasattr(obj, "user"):
                return obj.user == request.user
        elif user_role == "trader":
            # Traders can create/modify crops from farmers (business logic handled in views)
            return True

        return False


class TransactionPermission(permissions.BasePermission, UserStatusMixin):
    """
    Permission for transaction operations
    Business Rule: Any authenticated user can create transactions, users can only view their own
    """

    # TODO: NEED to use something like this please

    def has_permission(self, request, view):
        if not (request.user and request.user.is_authenticated):
            log_security_event_standardized(
                "UNAUTHENTICATED_TRANSACTION_ACCESS",
                "Unauthenticated user attempted to access transaction endpoint",
                request=request,
                metadata={
                    "view_name": view.__class__.__name__,
                    "endpoint": request.path,
                },
                level="WARNING",
            )
            return False

        # Check user status first
        is_valid, reason = self.check_user_status(request.user)
        if not is_valid:
            log_operation_info(
                "TRANSACTION_PERMISSION_USER_STATUS_INVALID",
                f"Transaction permission denied for {request.user.email}: {reason}",
                metadata={
                    "user_id": request.user.id,
                    "user_email": request.user.email,
                    "reason": reason,
                    "view_name": view.__class__.__name__,
                },
                level="WARNING",
            )
            return False

        user_role = request.user.role

        # All authenticated users can view and create transactions
        has_permission = user_role in ["farmer", "trader", "manufacturer", "admin"]

        # Log permission check result
        log_operation_info(
            "TRANSACTION_PERMISSION_CHECK",
            f"Transaction permission for {request.user.email}: {'granted' if has_permission else 'denied'}",
            metadata={
                "user_id": request.user.id,
                "user_email": request.user.email,
                "user_role": user_role,
                "permission_granted": has_permission,
                "view_name": view.__class__.__name__,
            },
            level="WARNING" if not has_permission else "INFO",
        )

        return has_permission

    def has_object_permission(self, request, view, obj):
        user_role = request.user.role

        # Admin can do everything
        if user_role == "admin" or request.user.is_staff:
            return True

        # Users can only access transactions they own or are involved in
        if hasattr(obj, "buyer") and hasattr(obj, "seller"):
            return request.user in [obj.buyer, obj.seller]
        elif hasattr(obj, "user"):
            return obj.user == request.user

        return False


class InventoryPermission(permissions.BasePermission, UserStatusMixin):
    """
    Permission for inventory operations
    Business Rule: Traders own inventory, manufacturers can view trader inventory
    """

    # TODO: NEED to use something like this please

    def has_permission(self, request, view):
        if not (request.user and request.user.is_authenticated):
            log_security_event_standardized(
                "UNAUTHENTICATED_INVENTORY_ACCESS",
                "Unauthenticated user attempted to access inventory endpoint",
                request=request,
                metadata={
                    "view_name": view.__class__.__name__,
                    "endpoint": request.path,
                },
                level="WARNING",
            )
            return False

        # Check user status first
        is_valid, reason = self.check_user_status(request.user)
        if not is_valid:
            log_operation_info(
                "INVENTORY_PERMISSION_USER_STATUS_INVALID",
                f"Inventory permission denied for {request.user.email}: {reason}",
                metadata={
                    "user_id": request.user.id,
                    "user_email": request.user.email,
                    "reason": reason,
                    "view_name": view.__class__.__name__,
                },
                level="WARNING",
            )
            return False

        user_role = request.user.role

        # Read operations - traders and manufacturers can view inventory
        if request.method in permissions.SAFE_METHODS:
            has_permission = user_role in ["trader", "manufacturer", "admin"]
        else:
            # Write operations - only traders can manage their own inventory
            has_permission = user_role in ["trader", "admin"]

        # Log permission check result
        log_operation_info( 
            "INVENTORY_PERMISSION_CHECK",
            f"Inventory permission for {request.user.email}: {'granted' if has_permission else 'denied'}",
            metadata={
                "user_id": request.user.id,
                "user_email": request.user.email,
                "user_role": user_role,
                "request_method": request.method,
                "permission_granted": has_permission,
                "view_name": view.__class__.__name__,
            },
            level="WARNING" if not has_permission else "INFO",
        )

        return has_permission

    def has_object_permission(self, request, view, obj):
        user_role = request.user.role

        # Admin can do everything
        if user_role == "admin" or request.user.is_staff:
            return True

        # Read operations
        if request.method in permissions.SAFE_METHODS:
            # Traders can view their own inventory, manufacturers can view any trader's inventory
            if user_role == "trader":
                if hasattr(obj, "trader"):
                    return obj.trader == request.user
                elif hasattr(obj, "owner"):
                    return obj.owner == request.user
                elif hasattr(obj, "user"):
                    return obj.user == request.user
            elif user_role == "manufacturer":
                # Manufacturers can view any trader's inventory for purchasing
                return True

        # Write operations - only traders can manage their own inventory
        else:
            if user_role == "trader":
                if hasattr(obj, "trader"):
                    return obj.trader == request.user
                elif hasattr(obj, "owner"):
                    return obj.owner == request.user
                elif hasattr(obj, "user"):
                    return obj.user == request.user

        return False


class ProofOfUnlockPermission(permissions.BasePermission, UserStatusMixin):
    """
    Permission for proof-of-unlock operations
    Business Rule: Manufacturers/traders can create, buyer-seller can view, both can update milestones
    """

    # TODO: NEED to use something like this please

    def has_permission(self, request, view):
        if not (request.user and request.user.is_authenticated):
            return False

        # Check user status first
        is_valid, reason = self.check_user_status(request.user)
        if not is_valid:
            log_operation_info(
                message=f"ProofOfUnlock permission denied for {request.user.email}: {reason}"
            )
            return False

        user_role = request.user.role

        # Only manufacturers, traders, and admins can access proof-of-unlock
        return user_role in ["manufacturer", "trader", "admin"]

    def has_object_permission(self, request, view, obj):
        user_role = request.user.role

        # Admin can do everything
        if user_role == "admin" or request.user.is_staff:
            return True

        # Users can only access proof-of-unlock transactions they're involved in
        if hasattr(obj, "buyer_id") and hasattr(obj, "seller_id"):
            return request.user.id in [obj.buyer_id, obj.seller_id]
        elif hasattr(obj, "buyer") and hasattr(obj, "seller"):
            return request.user in [obj.buyer, obj.seller]
        elif hasattr(obj, "user"):
            return obj.user == request.user

        return False

"""
Django management command to show OAuth2 token statistics
"""

from django.core.management.base import BaseCommand
from oauth2_auth.token_management import token_manager  # Legacy compatibility wrapper
import json


class Command(BaseCommand):
    help = 'Show OAuth2 token statistics and health information'

    def add_arguments(self, parser):
        parser.add_argument(
            '--format',
            type=str,
            choices=['table', 'json'],
            default='table',
            help='Output format (default: table)'
        )
        parser.add_argument(
            '--detailed',
            action='store_true',
            help='Show detailed statistics'
        )

    def handle(self, *args, **options):
        output_format = options['format']
        detailed = options['detailed']

        self.stdout.write('Fetching OAuth2 token statistics...')

        try:
            stats = token_manager.get_token_statistics()
            
            if 'error' in stats:
                self.stdout.write(
                    self.style.ERROR(f'Failed to get statistics: {stats["error"]}')
                )
                return

            if output_format == 'json':
                self.stdout.write(json.dumps(stats, indent=2, default=str))
            else:
                self._display_table_format(stats, detailed)

        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Failed to get token statistics: {str(e)}')
            )

    def _display_table_format(self, stats, detailed):
        """Display statistics in table format"""
        
        self.stdout.write(
            self.style.SUCCESS('\n=== OAuth2 Token Statistics ===')
        )
        
        # Access Tokens
        access_stats = stats['access_tokens']
        self.stdout.write(f'\n📱 Access Tokens:')
        self.stdout.write(f'  Total: {access_stats["total"]}')
        self.stdout.write(f'  Active: {access_stats["active"]} ({self._percentage(access_stats["active"], access_stats["total"])}%)')
        self.stdout.write(f'  Expired: {access_stats["expired"]} ({self._percentage(access_stats["expired"], access_stats["total"])}%)')
        
        # Refresh Tokens
        refresh_stats = stats['refresh_tokens']
        self.stdout.write(f'\n🔄 Refresh Tokens:')
        self.stdout.write(f'  Total: {refresh_stats["total"]}')
        self.stdout.write(f'  Active: {refresh_stats["active"]} ({self._percentage(refresh_stats["active"], refresh_stats["total"])}%)')
        self.stdout.write(f'  Expired: {refresh_stats["expired"]} ({self._percentage(refresh_stats["expired"], refresh_stats["total"])}%)')
        
        # Grants
        grant_stats = stats['grants']
        self.stdout.write(f'\n🎫 Authorization Grants:')
        self.stdout.write(f'  Total: {grant_stats["total"]}')
        self.stdout.write(f'  Active: {grant_stats["active"]} ({self._percentage(grant_stats["active"], grant_stats["total"])}%)')
        self.stdout.write(f'  Expired: {grant_stats["expired"]} ({self._percentage(grant_stats["expired"], grant_stats["total"])}%)')
        
        # Applications
        app_stats = stats['applications']
        self.stdout.write(f'\n🔧 Applications:')
        self.stdout.write(f'  Total: {app_stats["total"]}')
        
        # Users
        user_stats = stats['users']
        self.stdout.write(f'\n👥 Users:')
        self.stdout.write(f'  With active tokens: {user_stats["with_active_tokens"]}')
        
        # Cleanup Recommendations
        cleanup = stats['cleanup_recommendations']
        total_cleanup = sum(cleanup.values())
        
        if total_cleanup > 0:
            self.stdout.write(f'\n🧹 Cleanup Recommendations:')
            self.stdout.write(f'  Expired access tokens: {cleanup["expired_access_tokens"]}')
            self.stdout.write(f'  Expired refresh tokens: {cleanup["expired_refresh_tokens"]}')
            self.stdout.write(f'  Expired grants: {cleanup["expired_grants"]}')
            self.stdout.write(
                self.style.WARNING(f'  Total items to cleanup: {total_cleanup}')
            )
            self.stdout.write(
                self.style.SUCCESS('\n💡 Run "python manage.py cleanup_tokens" to clean up expired tokens')
            )
        else:
            self.stdout.write(
                self.style.SUCCESS('\n✅ No cleanup needed - all tokens are active')
            )
        
        # Health Assessment
        self._display_health_assessment(stats)

    def _display_health_assessment(self, stats):
        """Display system health assessment"""
        self.stdout.write(f'\n🏥 System Health Assessment:')
        
        access_stats = stats['access_tokens']
        refresh_stats = stats['refresh_tokens']
        cleanup = stats['cleanup_recommendations']
        
        health_score = 100
        issues = []
        
        # Check expired token ratio
        if access_stats['total'] > 0:
            expired_ratio = access_stats['expired'] / access_stats['total']
            if expired_ratio > 0.5:
                health_score -= 20
                issues.append('High ratio of expired access tokens')
            elif expired_ratio > 0.3:
                health_score -= 10
                issues.append('Moderate ratio of expired access tokens')
        
        # Check cleanup needs
        total_cleanup = sum(cleanup.values())
        if total_cleanup > 1000:
            health_score -= 15
            issues.append('Large number of items need cleanup')
        elif total_cleanup > 100:
            health_score -= 5
            issues.append('Some items need cleanup')
        
        # Check refresh token health
        if refresh_stats['total'] > 0:
            refresh_expired_ratio = refresh_stats['expired'] / refresh_stats['total']
            if refresh_expired_ratio > 0.7:
                health_score -= 10
                issues.append('High ratio of expired refresh tokens')
        
        # Display health score
        if health_score >= 90:
            status_style = self.style.SUCCESS
            status = 'EXCELLENT'
        elif health_score >= 75:
            status_style = self.style.SUCCESS
            status = 'GOOD'
        elif health_score >= 60:
            status_style = self.style.WARNING
            status = 'FAIR'
        else:
            status_style = self.style.ERROR
            status = 'POOR'
        
        self.stdout.write(f'  Health Score: {status_style(f"{health_score}/100 ({status})")}')
        
        if issues:
            self.stdout.write(f'  Issues:')
            for issue in issues:
                self.stdout.write(f'    - {issue}')
        else:
            self.stdout.write(f'  ✅ No issues detected')

    def _percentage(self, part, total):
        """Calculate percentage"""
        if total == 0:
            return 0
        return round((part / total) * 100, 1)

import logging
from rest_framework.decorators import (
    api_view,
    permission_classes,
    authentication_classes,
)
from oauth2_auth.authentication import (
    OAuth2Authentication,
    EnhancedSessionAuthentication,
    EnhancedTokenAuthentication,
)
from rest_framework.response import Response
from rest_framework import status
from decimal import Decimal
from django.db import transaction
from typing import Dict, Any
from rest_framework import permissions
from user.permissions import UserStatusMixin

logger = logging.getLogger(__name__)

from agritram.message_utils import (
    handle_serializer_errors,
    handle_exception_with_logging,
    StandardSuccessResponse,
)
from agritram.exceptions import (
    raise_validation_error,
    raise_authorization_error,
    raise_not_found_error,
    raise_business_logic_error,
)
from agritram.logger_utils import (
    log_request_info,
    log_response_info,
    log_operation_info,
    log_database_operation,
    log_business_event,
    log_security_event_standardized,
)


from crops.models import CropTransfer
from inventory.models import InventoryCropStatus, InventoryQuantity
from .serializers import ProofOfUnlockSerializer, MilestoneSerializer
from django.utils import timezone
from .models import Milestone, ProofOfUnlock
from django.shortcuts import get_object_or_404
from crops.serializers import CropTransferSerializer
from user.models import User


class ProofOfUnlockCreatePermission(permissions.BasePermission, UserStatusMixin):
    """
    Permission for creating proof of unlock transactions.
    Business Rule: Only manufacturers (buyers) and traders (sellers) can create.
    """

    def has_permission(self, request, view):
        if not (request.user and request.user.is_authenticated):
            return False

        # Check user status first
        is_valid, reason = self.check_user_status(request.user)
        if not is_valid:
            log_security_event_standardized(
                event_type="PERMISSION_DENIED",
                description=f"ProofOfUnlock create permission denied: {reason}",
                user=request.user,
                request=request,
                metadata={
                    "permission_type": "ProofOfUnlockCreatePermission",
                    "reason": reason,
                },
                level="WARNING",
            )
            return False

        user_role = request.user.role

        # Only manufacturers, traders, and admins can create proof of unlock
        return user_role in ["manufacturer", "trader", "admin"]


class ProofOfUnlockViewPermission(permissions.BasePermission, UserStatusMixin):
    """
    Permission for viewing proof of unlock transactions.
    Business Rule: Only the buyer and seller involved can view.
    """

    def has_permission(self, request, view):
        if not (request.user and request.user.is_authenticated):
            return False

        # Check user status first
        is_valid, reason = self.check_user_status(request.user)
        if not is_valid:
            log_security_event_standardized(
                event_type="PERMISSION_DENIED",
                description=f"ProofOfUnlock view permission denied: {reason}",
                user=request.user,
                request=request,
                metadata={
                    "permission_type": "ProofOfUnlockViewPermission",
                    "reason": reason,
                },
                level="WARNING",
            )
            return False

        user_role = request.user.role

        # Only manufacturers, traders, and admins can view proof of unlock
        return user_role in ["manufacturer", "trader", "admin"]

    def has_object_permission(self, request, view, obj):
        user_role = request.user.role

        # Admin can view everything
        if user_role == "admin":
            return True

        # Only buyer and seller can view the transaction
        if hasattr(obj, "buyer") and hasattr(obj, "seller"):
            return request.user in [obj.buyer, obj.seller]
        elif hasattr(obj, "buyer_id") and hasattr(obj, "seller_id"):
            return request.user.id in [obj.buyer_id, obj.seller_id]

        return False


class MilestoneUpdatePermission(permissions.BasePermission, UserStatusMixin):
    """
    Permission for updating milestones.
    Business Rule: Both buyer (manufacturer) and seller (trader) can update milestones.
    """

    def has_permission(self, request, view):
        if not (request.user and request.user.is_authenticated):
            return False

        # Check user status first
        is_valid, reason = self.check_user_status(request.user)
        if not is_valid:
            log_security_event_standardized(
                event_type="PERMISSION_DENIED",
                description=f"Milestone update permission denied: {reason}",
                user=request.user,
                request=request,
                metadata={
                    "permission_type": "MilestoneUpdatePermission",
                    "reason": reason,
                },
                level="WARNING",
            )
            return False

        user_role = request.user.role

        # Only manufacturers, traders, and admins can update milestones
        return user_role in ["manufacturer", "trader", "admin"]


# Create your views here.
@api_view(["POST"])
@permission_classes(
    [ProofOfUnlockCreatePermission]
)  # Manufacturers and traders can create
@authentication_classes(
    [OAuth2Authentication, EnhancedSessionAuthentication, EnhancedTokenAuthentication]
)
def create_proof_of_unlock(request) -> Response:
    """
    Create a new proof of unlock transaction.
    Business Rule: Only manufacturers (buyers) and traders (sellers) can create.
    Business Flow: Manufacturer creates proof of unlock when buying from trader.
    """

    # Log incoming request
    log_request_info(request, "PROOF_OF_UNLOCK_CREATE_REQUEST")

    log_operation_info(
        operation_type="PROOF_OF_UNLOCK_CREATE_START",
        message="Starting proof of unlock creation",
        metadata={
            "user_id": request.user.id,
            "user_email": request.user.email,
            "user_role": request.user.role,
        },
    )

    try:
        # Validate user role for creation
        if request.user.role not in ["manufacturer", "trader", "admin"]:
            log_security_event_standardized(
                event_type="UNAUTHORIZED_ACCESS_ATTEMPT",
                description="Unauthorized attempt to create proof of unlock transaction",
                user=request.user,
                request=request,
                metadata={
                    "required_roles": ["manufacturer", "trader", "admin"],
                    "user_role": request.user.role,
                },
                level="WARNING",
            )
            raise_authorization_error(
                message="Only manufacturers and traders can create proof of unlock transactions",
                details="This operation is restricted to manufacturer and trader roles",
            )

        # Prepare request data with default values
        request_data = _prepare_request_data(request.data)

        # Validate and create crop transfer
        crop_transfer = _create_crop_transfer(request_data)
        if not crop_transfer:
            raise_validation_error(
                message="Invalid crop transfer data",
                details="Failed to create crop transfer with provided data",
            )

        # Process the transaction within a database transaction
        with transaction.atomic():
            # Create proof of unlock
            proof_of_unlock = _create_proof_of_unlock(request_data)

            # Update inventory
            _update_inventory(request_data)

            # Log successful creation
            log_business_event(
                event_type="PROOF_OF_UNLOCK_CREATED",
                description=f"New proof of unlock transaction created with ID: {proof_of_unlock.tx_id}",
                entity_type="PROOF_OF_UNLOCK",
                entity_id=proof_of_unlock.tx_id,
                metadata={
                    "buyer_id": proof_of_unlock.buyer_id,
                    "seller_id": proof_of_unlock.seller_id,
                    "total_amount": str(proof_of_unlock.total_amount),
                    "crop_count": len(request_data.get("crops", [])),
                },
            )

            response_data = ProofOfUnlockSerializer(proof_of_unlock).data

            # Log response
            log_response_info(
                response_data=response_data,
                status_code=201,
                operation_type="PROOF_OF_UNLOCK_CREATE_SUCCESS",
            )

            return StandardSuccessResponse.record_created(
                message="Proof of unlock created successfully",
                details=f"New proof of unlock transaction created with ID: {proof_of_unlock.tx_id}",
                record_data=response_data,
            )

    except User.DoesNotExist:
        log_operation_info(
            operation_type="PROOF_OF_UNLOCK_CREATE_FAILURE",
            message="User not found during proof of unlock creation",
            metadata={"error": "User.DoesNotExist"},
            level="ERROR",
        )
        raise_not_found_error(
            message="Seller with the provided account address not found",
            details="No user found with the provided seller account address",
            resource_type="user",
        )
    except Exception as e:
        log_operation_info(
            operation_type="PROOF_OF_UNLOCK_CREATE_FAILURE",
            message=f"Proof of unlock creation failed: {str(e)}",
            metadata={"error": str(e)},
            level="ERROR",
        )
        return handle_exception_with_logging(e, "proof of unlock creation")


def _prepare_request_data(data: Dict[str, Any]) -> Dict[str, Any]:
    """Prepare and validate request data with default values."""
    data["created_at"] = timezone.now()
    data["last_updated"] = timezone.now()
    data.setdefault("release_amount", 0)
    data.setdefault("status", "active")
    return data


def _create_crop_transfer(data: Dict[str, Any]) -> bool:
    """Create and validate crop transfer."""
    try:
        crop_transfer = CropTransfer.objects.create(
            transaction_id=data["tx_id"],
            crop_id=data["crops"][0]["crop_id"],
            from_user=User.objects.get(account_address=data["seller"]),
            to_user=User.objects.get(account_address=data["buyer"]),
            cost=data["total_amount"],
            status="PENDING",
        )

        log_database_operation(
            operation_type="CREATE",
            table_name="crops_croptransfer",
            operation_result="SUCCESS",
            metadata={
                "transaction_id": data["tx_id"],
                "crop_id": data["crops"][0]["crop_id"],
                "from_user": data["seller"],
                "to_user": data["buyer"],
                "cost": str(data["total_amount"]),
            },
        )

        if crop_transfer:
            return True
        return False
    except Exception as e:
        log_database_operation(
            operation_type="CREATE",
            table_name="crops_croptransfer",
            operation_result="FAILURE",
            metadata={"error": str(e), "transaction_id": data.get("tx_id")},
            level="ERROR",
        )
        raise


def _create_proof_of_unlock(data: Dict[str, Any]) -> ProofOfUnlock:
    """Create proof of unlock transaction."""
    serializer = ProofOfUnlockSerializer(data=data)
    if not serializer.is_valid():
        raise ValueError(serializer.errors)
    return serializer.save()


def _update_inventory(data: Dict[str, Any]) -> None:
    """Update inventory quantities and status."""
    seller = User.objects.get(account_address=data["seller"])

    quantity = Decimal(str(data["crops"][0]["quantity"]))
    crop_id = data["crops"][0]["crop_id"]

    # Update inventory quantity
    inventory_quantity = InventoryQuantity.objects.get(
        trader=seller.id,
    )
    inventory_quantity.ready_to_sell_quantity -= quantity
    inventory_quantity.ready_to_sell_batches -= 1
    inventory_quantity.sold_quantity += quantity
    inventory_quantity.sold_batches += 1
    inventory_quantity.save()

    # Update inventory crop status
    inventory_crop_status = InventoryCropStatus.objects.select_related("crop").get(
        trader=seller.id, crop=crop_id
    )
    inventory_crop_status.status = "in_progress"
    inventory_crop_status.save()


@api_view(["GET"])
@permission_classes(
    [ProofOfUnlockViewPermission]
)  # Only buyer and seller involved can view
@authentication_classes(
    [OAuth2Authentication, EnhancedSessionAuthentication, EnhancedTokenAuthentication]
)
def get_proof_of_unlock_by_user(request):
    """
    Retrieve proof of unlock transactions for the authenticated user.
    Business Rule: Only the buyer and seller involved can view their transactions.
    """

    # Log incoming request
    log_request_info(request, "PROOF_OF_UNLOCK_GET_REQUEST")

    log_operation_info(
        operation_type="PROOF_OF_UNLOCK_GET_START",
        message="Starting proof of unlock retrieval",
        metadata={
            "user_id": request.user.id,
            "user_email": request.user.email,
            "user_role": request.user.role,
        },
    )

    try:
        # Filter transactions where the user is either the buyer or the seller
        if request.user.role == "manufacturer":
            # Manufacturers see transactions where they are the buyer
            transactions = ProofOfUnlock.objects.filter(
                buyer_id=request.user.id
            ).order_by("-created_at")

            log_database_operation(
                operation_type="SELECT",
                table_name="proof_of_unlock_proofofunlock",
                operation_result="SUCCESS",
                metadata={
                    "filter_type": "buyer_transactions",
                    "buyer_id": request.user.id,
                    "count": transactions.count(),
                },
            )

        elif request.user.role == "trader":
            # Traders see transactions where they are the seller
            transactions = ProofOfUnlock.objects.filter(
                seller_id=request.user.id
            ).order_by("-created_at")

            log_database_operation(
                operation_type="SELECT",
                table_name="proof_of_unlock_proofofunlock",
                operation_result="SUCCESS",
                metadata={
                    "filter_type": "seller_transactions",
                    "seller_id": request.user.id,
                    "count": transactions.count(),
                },
            )

        elif request.user.role == "admin":
            # Admins can see all transactions
            transactions = ProofOfUnlock.objects.all().order_by("-created_at")

            log_database_operation(
                operation_type="SELECT",
                table_name="proof_of_unlock_proofofunlock",
                operation_result="SUCCESS",
                metadata={
                    "filter_type": "all_transactions",
                    "count": transactions.count(),
                },
            )

        else:
            log_security_event_standardized(
                event_type="UNAUTHORIZED_ACCESS_ATTEMPT",
                description="Unauthorized attempt to access proof of unlock transactions",
                user=request.user,
                request=request,
                metadata={
                    "required_roles": ["manufacturer", "trader", "admin"],
                    "user_role": request.user.role,
                },
                level="WARNING",
            )
            raise_authorization_error(
                message="Only manufacturers, traders, and admins can access proof of unlock transactions",
                details="This operation is restricted to manufacturer, trader, and admin roles",
            )

        serializer = ProofOfUnlockSerializer(transactions, many=True)

        # Log successful retrieval
        log_business_event(
            event_type="PROOF_OF_UNLOCK_RETRIEVED",
            description=f"Proof of unlock transactions retrieved for user {request.user.email}",
            entity_type="PROOF_OF_UNLOCK",
            entity_id=None,
            metadata={
                "user_id": request.user.id,
                "user_role": request.user.role,
                "transaction_count": len(serializer.data),
            },
        )

        response_data = serializer.data

        # Log response
        log_response_info(
            response_data={"transaction_count": len(response_data)},
            status_code=200,
            operation_type="PROOF_OF_UNLOCK_GET_SUCCESS",
        )

        return StandardSuccessResponse.data_retrieved(
            message="Proof of unlock transactions retrieved successfully",
            details=f"Retrieved {len(serializer.data)} proof of unlock transactions for user",
            data=serializer.data,
        )

    except ProofOfUnlock.DoesNotExist:
        log_operation_info(
            operation_type="PROOF_OF_UNLOCK_GET_FAILURE",
            message="No proof of unlock transactions found for user",
            metadata={
                "user_id": request.user.id,
                "user_role": request.user.role,
                "error": "ProofOfUnlock.DoesNotExist",
            },
            level="INFO",
        )
        raise_not_found_error(
            message="No transactions found for this user",
            details="No proof of unlock transactions found for the authenticated user",
            resource_type="proof_of_unlock",
        )
    except Exception as e:
        log_operation_info(
            operation_type="PROOF_OF_UNLOCK_GET_FAILURE",
            message=f"Proof of unlock retrieval failed: {str(e)}",
            metadata={"error": str(e)},
            level="ERROR",
        )
        return handle_exception_with_logging(e, "proof of unlock retrieval")


@api_view(["PUT"])
@permission_classes(
    [MilestoneUpdatePermission]
)  # Both buyer and seller can update milestones
@authentication_classes(
    [OAuth2Authentication, EnhancedSessionAuthentication, EnhancedTokenAuthentication]
)
def update_milestone(request):
    """
    Update milestone status and transaction status.
    Business Rule: Both buyer (manufacturer) and seller (trader) can update milestones.
    Business Flow: Trader updates milestones as they fulfill delivery, Manufacturer releases payments.

    Expected request data:
    {
        "milestone": {
            "id": "milestone_id",
            "status": "new_status"
        },
        "tx_id": "transaction_id",
        "status": "new_transaction_status"
    }
    """

    # Log incoming request
    log_request_info(request, "MILESTONE_UPDATE_REQUEST")

    log_operation_info(
        operation_type="MILESTONE_UPDATE_START",
        message="Starting milestone update",
        metadata={
            "user_id": request.user.id,
            "user_email": request.user.email,
            "user_role": request.user.role,
            "milestone_id": request.data.get("milestone", {}).get("id"),
            "tx_id": request.data.get("tx_id"),
        },
    )

    try:
        # Get milestone and transaction
        milestone = get_object_or_404(Milestone, id=request.data["milestone"]["id"])
        transaction_obj: ProofOfUnlock = get_object_or_404(
            ProofOfUnlock, tx_id=request.data["tx_id"]
        )

        log_database_operation(
            operation_type="SELECT",
            table_name="proof_of_unlock_milestone",
            operation_result="SUCCESS",
            metadata={
                "milestone_id": milestone.id,
                "milestone_status": milestone.status,
            },
        )

        log_database_operation(
            operation_type="SELECT",
            table_name="proof_of_unlock_proofofunlock",
            operation_result="SUCCESS",
            metadata={
                "tx_id": transaction_obj.tx_id,
                "transaction_status": transaction_obj.status,
            },
        )

        # Verify milestone belongs to the transaction
        if milestone.transaction != transaction_obj:
            log_operation_info(
                operation_type="MILESTONE_UPDATE_VALIDATION_FAILURE",
                message="Milestone does not belong to the specified transaction",
                metadata={
                    "milestone_id": milestone.id,
                    "milestone_transaction_id": (
                        milestone.transaction.tx_id if milestone.transaction else None
                    ),
                    "requested_transaction_id": transaction_obj.tx_id,
                },
                level="WARNING",
            )
            raise_validation_error(
                message="Milestone does not belong to the specified transaction",
                details="The milestone ID provided is not associated with the specified transaction",
            )

        # Verify user is involved in the transaction
        if request.user.role not in ["admin"] and request.user.id not in [
            transaction_obj.buyer_id,
            transaction_obj.seller_id,
        ]:
            log_security_event_standardized(
                event_type="UNAUTHORIZED_ACCESS_ATTEMPT",
                description="Unauthorized attempt to update milestone",
                user=request.user,
                request=request,
                metadata={
                    "milestone_id": milestone.id,
                    "transaction_id": transaction_obj.tx_id,
                    "transaction_buyer_id": transaction_obj.buyer_id,
                    "transaction_seller_id": transaction_obj.seller_id,
                    "user_role": request.user.role,
                },
                level="WARNING",
            )
            raise_authorization_error(
                message="You can only update milestones for transactions you're involved in",
                details="Only the buyer, seller, or admin can update milestones for this transaction",
            )

        # Update milestone status
        old_status = milestone.status
        milestone.status = request.data["milestone"]["status"]
        if request.data["milestone"]["status"] == "completed":
            milestone.completed_date = timezone.now()
        milestone.save()

        log_database_operation(
            operation_type="UPDATE",
            table_name="proof_of_unlock_milestone",
            operation_result="SUCCESS",
            metadata={
                "milestone_id": milestone.id,
                "old_status": old_status,
                "new_status": milestone.status,
                "completed_date": (
                    milestone.completed_date.isoformat()
                    if milestone.completed_date
                    else None
                ),
            },
        )

        # Handle payment release (typically done by manufacturer/buyer)
        if request.data["milestone"]["status"] == "released":
            if request.user.role == "manufacturer" or request.user.role == "admin":
                old_release_amount = transaction_obj.release_amount
                transaction_obj.release_amount = (
                    transaction_obj.total_amount + milestone.amount
                )
                transaction_obj.save()

                log_business_event(
                    event_type="PAYMENT_RELEASED",
                    description=f"Payment released for milestone {milestone.id}",
                    entity_type="MILESTONE",
                    entity_id=milestone.id,
                    metadata={
                        "transaction_id": transaction_obj.tx_id,
                        "milestone_amount": str(milestone.amount),
                        "old_release_amount": str(old_release_amount),
                        "new_release_amount": str(transaction_obj.release_amount),
                        "released_by": request.user.email,
                    },
                )

            else:
                log_security_event_standardized(
                    event_type="UNAUTHORIZED_PAYMENT_RELEASE_ATTEMPT",
                    description="Unauthorized attempt to release payment",
                    user=request.user,
                    request=request,
                    metadata={
                        "milestone_id": milestone.id,
                        "transaction_id": transaction_obj.tx_id,
                        "user_role": request.user.role,
                    },
                    level="WARNING",
                )
                raise_authorization_error(
                    message="Only manufacturers can release payments",
                    details="Payment release is restricted to manufacturer role users",
                )

        # Update transaction status if provided
        if "status" in request.data and request.data["status"] == "completed":
            old_transaction_status = transaction_obj.status
            transaction_obj.status = request.data["status"]
            transaction_obj.last_updated = timezone.now()
            transaction_obj.save()

            log_database_operation(
                operation_type="UPDATE",
                table_name="proof_of_unlock_proofofunlock",
                operation_result="SUCCESS",
                metadata={
                    "tx_id": transaction_obj.tx_id,
                    "old_status": old_transaction_status,
                    "new_status": transaction_obj.status,
                    "last_updated": transaction_obj.last_updated.isoformat(),
                },
            )

            # Update inventory crop status
            try:
                inventory_crop_status = (
                    InventoryCropStatus.objects.select_for_update().get(
                        trader=transaction_obj.seller.id,
                        crop=transaction_obj.crops[0].crop_id,
                    )
                )
                old_inventory_status = inventory_crop_status.status
                inventory_crop_status.status = "sold"
                inventory_crop_status.save()

                log_database_operation(
                    operation_type="UPDATE",
                    table_name="inventory_inventorycropstatus",
                    operation_result="SUCCESS",
                    metadata={
                        "trader_id": transaction_obj.seller.id,
                        "crop_id": transaction_obj.crops[0].crop_id,
                        "old_status": old_inventory_status,
                        "new_status": "sold",
                    },
                )

            except (InventoryCropStatus.DoesNotExist, IndexError, AttributeError) as e:
                log_operation_info(
                    operation_type="INVENTORY_STATUS_UPDATE_SKIPPED",
                    message="Inventory crop status update skipped",
                    metadata={
                        "error": str(e),
                        "transaction_id": transaction_obj.tx_id,
                    },
                    level="WARNING",
                )

            # Update crop transfer status
            try:
                crop_transfer = CropTransfer.objects.select_for_update().get(
                    transaction_id=transaction_obj.tx_id,
                    crop_id=transaction_obj.crops[0].crop_id,
                )
                old_crop_transfer_status = crop_transfer.status
                crop_transfer.status = "COMPLETED"
                crop_transfer.save()

                log_database_operation(
                    operation_type="UPDATE",
                    table_name="crops_croptransfer",
                    operation_result="SUCCESS",
                    metadata={
                        "transaction_id": transaction_obj.tx_id,
                        "crop_id": transaction_obj.crops[0].crop_id,
                        "old_status": old_crop_transfer_status,
                        "new_status": "COMPLETED",
                    },
                )

            except (CropTransfer.DoesNotExist, IndexError, AttributeError) as e:
                log_operation_info(
                    operation_type="CROP_TRANSFER_STATUS_UPDATE_SKIPPED",
                    message="Crop transfer status update skipped",
                    metadata={
                        "error": str(e),
                        "transaction_id": transaction_obj.tx_id,
                    },
                    level="WARNING",
                )

            # Log transaction completion
            log_business_event(
                event_type="TRANSACTION_COMPLETED",
                description=f"Proof of unlock transaction completed: {transaction_obj.tx_id}",
                entity_type="PROOF_OF_UNLOCK",
                entity_id=transaction_obj.tx_id,
                metadata={
                    "buyer_id": transaction_obj.buyer_id,
                    "seller_id": transaction_obj.seller_id,
                    "total_amount": str(transaction_obj.total_amount),
                    "release_amount": str(transaction_obj.release_amount),
                    "completed_by": request.user.email,
                },
            )

        # Return updated milestone data
        serializer = MilestoneSerializer(milestone)

        # Log successful milestone update
        log_business_event(
            event_type="MILESTONE_UPDATED",
            description=f"Milestone {milestone.id} updated successfully",
            entity_type="MILESTONE",
            entity_id=milestone.id,
            metadata={
                "milestone_status": milestone.status,
                "transaction_id": transaction_obj.tx_id,
                "updated_by": request.user.email,
            },
        )

        response_data = serializer.data

        # Log response
        log_response_info(
            response_data=response_data,
            status_code=200,
            operation_type="MILESTONE_UPDATE_SUCCESS",
        )

        return StandardSuccessResponse.record_updated(
            message="Milestone updated successfully",
            details=f"Milestone {milestone.id} status updated to '{milestone.status}'",
            record_data=serializer.data,
        )

    except KeyError as e:
        log_operation_info(
            operation_type="MILESTONE_UPDATE_FAILURE",
            message=f"Missing required field: {str(e)}",
            metadata={"error": str(e)},
            level="ERROR",
        )
        raise_validation_error(
            message=f"Missing required field: {str(e)}",
            details="Required field is missing from the request data",
        )
    except Exception as e:
        log_operation_info(
            operation_type="MILESTONE_UPDATE_FAILURE",
            message=f"Milestone update failed: {str(e)}",
            metadata={"error": str(e)},
            level="ERROR",
        )
        return handle_exception_with_logging(e, "milestone update")

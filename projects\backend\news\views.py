from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from rest_framework import status
from .serializers import NewsSerializer
from .models import News
from django.shortcuts import get_object_or_404
from oauth2_auth.permissions import ReadOnlyPermission, AdminPermission
from agritram.message_utils import handle_serializer_errors, handle_exception_with_logging, StandardSuccessResponse
from agritram.exceptions import raise_not_found_error

@api_view(['POST'])
@permission_classes([AdminPermission])
def create_news(request):
    try:
        serializer = NewsSerializer(data=request.data)
        if serializer.is_valid():
            news_article = serializer.save()
            return StandardSuccessResponse.record_created(
                message="News article created successfully",
                details=f"New news article created with ID: {news_article.id}",
                record_data=serializer.data
            )
        return handle_serializer_errors(serializer)
    except Exception as e:
        return handle_exception_with_logging(e, "news creation")

@api_view(['GET'])
@permission_classes([ReadOnlyPermission])
def get_all_news(request):
    try:
        news = News.objects.all()
        serializer = NewsSerializer(news, many=True)
        return StandardSuccessResponse.data_retrieved(
            message="All news articles retrieved successfully",
            details=f"Retrieved {len(serializer.data)} news articles",
            data=serializer.data
        )
    except Exception as e:
        return handle_exception_with_logging(e, "news retrieval")

@api_view(['GET'])
@permission_classes([ReadOnlyPermission])
def get_news_by_id(request, pk):
    try:
        news = get_object_or_404(News, pk=pk)
        serializer = NewsSerializer(news)
        return StandardSuccessResponse.data_retrieved(
            message="News article retrieved successfully",
            details=f"Retrieved news article with ID: {pk}",
            data=serializer.data
        )
    except News.DoesNotExist:
        raise_not_found_error(
            message="News not found",
            details=f"No news article found with ID {pk}",
            resource_type="news",
            resource_id=str(pk)
        )
    except Exception as e:
        return handle_exception_with_logging(e, "news retrieval by ID")
